# Troubleshooting Guide

> 🔧 **Common issues and solutions for Pazogen development and deployment**

## Quick Diagnostics

```mermaid
flowchart TD
    A[Issue Reported] --> B{Development or Production?}
    
    B -->|Development| C[Check Console Errors]
    B -->|Production| D[Check Network Tab]
    
    C --> E{Build Errors?}
    E -->|Yes| F[Check Dependencies]
    E -->|No| G[Check Runtime Errors]
    
    D --> H{API Errors?}
    H -->|Yes| I[Check Environment Variables]
    H -->|No| J[Check Bundle Loading]
    
    F --> K[npm install & restart]
    G --> L[Check Browser Console]
    I --> M[Verify API Keys]
    J --> N[Check CDN/Hosting]
    
    style A fill:#ffebee
    style K fill:#e8f5e8
    style L fill:#e8f5e8
    style M fill:#e8f5e8
    style N fill:#e8f5e8
```

## Development Issues

### Build Errors

#### Issue: `npm install` fails
```bash
# Error message
npm ERR! peer dep missing: react@^18.0.0

# Solution
npm install --legacy-peer-deps
# or
npm install --force

# Clean install
rm -rf node_modules package-lock.json
npm install
```

#### Issue: TypeScript compilation errors
```bash
# Error message
TS2307: Cannot find module './types' or its type declarations

# Solution 1: Check file paths
# Ensure import paths are correct and files exist

# Solution 2: Update TypeScript configuration
# tsconfig.json
{
  "compilerOptions": {
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true
  }
}

# Solution 3: Restart TypeScript server
# In VS Code: Ctrl+Shift+P -> "TypeScript: Restart TS Server"
```

#### Issue: Vite build fails
```bash
# Error message
[vite]: Rollup failed to resolve import

# Solution 1: Check import statements
# Ensure all imports use correct paths
import Component from './Component'; // ✅ Correct
import Component from 'Component'; // ❌ Incorrect

# Solution 2: Clear Vite cache
rm -rf node_modules/.vite
npm run dev

# Solution 3: Check vite.config.ts
export default defineConfig({
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    }
  }
});
```

### Runtime Errors

#### Issue: White screen on development
```typescript
// Check browser console for errors
// Common causes:

// 1. JavaScript errors in components
const Component = () => {
  // Error: Cannot read property of undefined
  const data = props.data.items; // ❌ Unsafe
  const data = props.data?.items || []; // ✅ Safe
  
  return <div>{data.map(item => <span key={item.id}>{item.name}</span>)}</div>;
};

// 2. Missing error boundaries
// Wrap components with ErrorBoundary
<ErrorBoundary>
  <App />
</ErrorBoundary>
```

#### Issue: Animations not working
```typescript
// Check Framer Motion setup
import { motion } from 'framer-motion';

// Common issues:
// 1. Missing motion wrapper
<div animate={{ x: 100 }} /> // ❌ Won't work
<motion.div animate={{ x: 100 }} /> // ✅ Correct

// 2. Invalid animation properties
<motion.div animate={{ width: 'auto' }} /> // ❌ Can cause issues
<motion.div animate={{ width: '100px' }} /> // ✅ Better

// 3. Reduced motion preference
const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
const animation = prefersReducedMotion ? {} : { x: 100 };
```

### Environment Issues

#### Issue: Environment variables not loading
```bash
# Check file naming
.env.local          # ✅ Correct for local development
.env.development    # ✅ Correct for development
.env               # ❌ Not recommended for sensitive data

# Check variable naming (Vite)
VITE_API_KEY=123   # ✅ Correct - accessible in client
API_KEY=123        # ❌ Not accessible in client code

# Check variable usage
const apiKey = import.meta.env.VITE_API_KEY; // ✅ Vite
const apiKey = process.env.REACT_APP_API_KEY; // ❌ Create React App syntax
```

## Production Issues

### Deployment Errors

#### Issue: Build succeeds but site doesn't load
```bash
# Check build output
npm run build
ls -la dist/

# Common issues:
# 1. Missing index.html
# 2. Incorrect asset paths
# 3. Missing environment variables

# Solution: Check build configuration
# vite.config.ts
export default defineConfig({
  base: '/', // Ensure correct base path
  build: {
    outDir: 'dist',
    assetsDir: 'assets'
  }
});
```

#### Issue: 404 errors on page refresh (SPA routing)
```bash
# Netlify: Create _redirects file
/* /index.html 200

# Apache: Create .htaccess file
RewriteEngine On
RewriteRule ^(?!.*\.).*$ /index.html [L]

# Nginx: Update configuration
location / {
  try_files $uri $uri/ /index.html;
}
```

### API Integration Issues

#### Issue: EmailJS not sending emails
```typescript
// Check configuration
const emailConfig = {
  serviceId: import.meta.env.VITE_EMAILJS_SERVICE_ID, // Check this exists
  templateId: import.meta.env.VITE_EMAILJS_TEMPLATE_ID, // Check this exists
  publicKey: import.meta.env.VITE_EMAILJS_PUBLIC_KEY // Check this exists
};

// Debug email sending
const sendEmail = async (data) => {
  try {
    console.log('Sending email with config:', emailConfig);
    console.log('Email data:', data);
    
    const response = await emailjs.send(
      emailConfig.serviceId,
      emailConfig.templateId,
      data,
      emailConfig.publicKey
    );
    
    console.log('Email response:', response);
    return response.status === 200;
  } catch (error) {
    console.error('Email error:', error);
    return false;
  }
};

// Common issues:
// 1. Incorrect service ID or template ID
// 2. Missing public key
// 3. Template variables don't match data keys
// 4. CORS issues (check EmailJS dashboard)
```

#### Issue: Google Maps not loading
```typescript
// Check API key and restrictions
const mapConfig = {
  apiKey: import.meta.env.VITE_GOOGLE_MAPS_API_KEY,
  libraries: ['places', 'geometry'] as const
};

// Debug map loading
useEffect(() => {
  if (!mapConfig.apiKey) {
    console.error('Google Maps API key is missing');
    return;
  }
  
  console.log('Loading Google Maps with key:', mapConfig.apiKey.substring(0, 10) + '...');
}, []);

// Common issues:
// 1. API key not set or incorrect
// 2. API key restrictions (check Google Cloud Console)
// 3. Billing not enabled
// 4. Required APIs not enabled (Maps JavaScript API, Places API)
```

### Performance Issues

#### Issue: Slow page load times
```typescript
// Check bundle size
npm run build:analyze

// Common solutions:
// 1. Implement code splitting
const HeavyComponent = lazy(() => import('./HeavyComponent'));

// 2. Optimize images
const OptimizedImage = ({ src, alt }) => (
  <img
    src={src}
    alt={alt}
    loading="lazy"
    decoding="async"
    style={{ aspectRatio: '16/9' }} // Prevent layout shift
  />
);

// 3. Remove unused dependencies
npm uninstall unused-package

// 4. Use production build
npm run build
npm run preview
```

#### Issue: Memory leaks
```typescript
// Common causes and solutions:

// 1. Event listeners not cleaned up
useEffect(() => {
  const handleResize = () => {
    // Handle resize
  };
  
  window.addEventListener('resize', handleResize);
  
  // ✅ Always clean up
  return () => {
    window.removeEventListener('resize', handleResize);
  };
}, []);

// 2. Timers not cleared
useEffect(() => {
  const timer = setInterval(() => {
    // Do something
  }, 1000);
  
  // ✅ Clear timer
  return () => clearInterval(timer);
}, []);

// 3. Subscriptions not unsubscribed
useEffect(() => {
  const subscription = observable.subscribe(data => {
    // Handle data
  });
  
  // ✅ Unsubscribe
  return () => subscription.unsubscribe();
}, []);
```

## Browser-Specific Issues

### Safari Issues

#### Issue: CSS Grid not working properly
```css
/* Safari-specific fixes */
.grid-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  
  /* Safari fallback */
  display: -webkit-grid;
  -webkit-grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}
```

#### Issue: Smooth scrolling not working
```css
/* Safari smooth scrolling fix */
html {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch; /* iOS Safari */
}
```

### Mobile Issues

#### Issue: Touch events not working
```typescript
// Ensure passive event listeners
useEffect(() => {
  const handleTouchStart = (e) => {
    // Handle touch
  };
  
  // ✅ Use passive for better performance
  document.addEventListener('touchstart', handleTouchStart, { passive: true });
  
  return () => {
    document.removeEventListener('touchstart', handleTouchStart);
  };
}, []);
```

#### Issue: Viewport issues on mobile
```html
<!-- Ensure correct viewport meta tag -->
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
```

## Debugging Tools

### Development Tools

```typescript
// Performance monitoring
const PerformanceDebugger = () => {
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      // Log render times
      console.time('Component Render');
      
      return () => {
        console.timeEnd('Component Render');
      };
    }
  });
  
  return null;
};

// Error logging
const ErrorLogger = ({ error, errorInfo }) => {
  useEffect(() => {
    if (error) {
      console.group('🚨 Error Details');
      console.error('Error:', error);
      console.error('Error Info:', errorInfo);
      console.error('Stack Trace:', error.stack);
      console.groupEnd();
    }
  }, [error, errorInfo]);
  
  return null;
};
```

### Production Debugging

```typescript
// Safe production logging
const logError = (error, context = {}) => {
  if (process.env.NODE_ENV === 'production') {
    // Send to error tracking service
    console.error('Production Error:', {
      message: error.message,
      stack: error.stack,
      context,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    });
  } else {
    console.error('Development Error:', error, context);
  }
};
```

## Getting Help

### Before Asking for Help

1. **Check the console** for error messages
2. **Search existing issues** in the repository
3. **Try the latest version** of dependencies
4. **Test in incognito mode** to rule out extensions
5. **Test on different browsers** to isolate browser-specific issues

### How to Report Issues

```markdown
## Bug Report Template

**Environment:**
- OS: [e.g., macOS 12.0]
- Browser: [e.g., Chrome 96.0]
- Node.js: [e.g., 18.17.0]
- npm: [e.g., 9.6.7]

**Steps to Reproduce:**
1. Go to '...'
2. Click on '...'
3. Scroll down to '...'
4. See error

**Expected Behavior:**
A clear description of what you expected to happen.

**Actual Behavior:**
A clear description of what actually happened.

**Screenshots:**
If applicable, add screenshots to help explain your problem.

**Console Errors:**
```
Paste any console errors here
```

**Additional Context:**
Add any other context about the problem here.
```

### Community Resources

- 📧 **Email Support**: [<EMAIL>](mailto:<EMAIL>)
- 💬 **WhatsApp**: +27659643597
- 🐛 **Bug Reports**: GitHub Issues
- 📚 **Documentation**: [[README|Main Documentation]]

---

> 💡 **Tip**: When troubleshooting, always check the browser's developer tools console first. Most issues will show error messages there that can guide you to the solution.
