# Testing Guide

> 🧪 **Comprehensive testing strategy and guidelines for Pazogen**

## Testing Strategy Overview

```mermaid
graph TB
    A[Testing Strategy] --> B[Unit Testing]
    A --> C[Integration Testing]
    A --> D[E2E Testing]
    A --> E[Performance Testing]
    A --> F[Accessibility Testing]
    
    B --> G[Components]
    B --> H[Hooks]
    B --> I[Utilities]
    
    C --> J[API Integration]
    C --> K[Form Workflows]
    C --> L[Navigation]
    
    D --> M[User Journeys]
    D --> N[Cross-browser]
    D --> O[Mobile Testing]
    
    E --> P[Load Testing]
    E --> Q[Bundle Analysis]
    E --> R[Core Web Vitals]
    
    F --> S[Screen Readers]
    F --> T[Keyboard Navigation]
    F --> U[Color Contrast]
    
    style A fill:#e3f2fd
    style B fill:#e8f5e8
    style C fill:#fff3e0
    style D fill:#f3e5f5
    style E fill:#fce4ec
    style F fill:#f1f8e9
```

## Testing Framework Setup

### Jest Configuration

```javascript
// jest.config.js
module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/src/setupTests.ts'],
  moduleNameMapping: {
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
    '\\.(jpg|jpeg|png|gif|svg)$': '<rootDir>/__mocks__/fileMock.js'
  },
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/main.tsx',
    '!src/vite-env.d.ts'
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  }
};
```

### Testing Library Setup

```typescript
// src/setupTests.ts
import '@testing-library/jest-dom';
import { configure } from '@testing-library/react';

// Configure testing library
configure({ testIdAttribute: 'data-testid' });

// Mock IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
  constructor() {}
  observe() { return null; }
  disconnect() { return null; }
  unobserve() { return null; }
};

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
  constructor() {}
  observe() { return null; }
  disconnect() { return null; }
  unobserve() { return null; }
};
```

## Unit Testing

### Component Testing

```typescript
// Button.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { ThemeProvider } from '../context/ThemeContext';
import Button from '../components/ui/Button';

const renderWithTheme = (component: React.ReactElement) => {
  return render(
    <ThemeProvider>
      {component}
    </ThemeProvider>
  );
};

describe('Button Component', () => {
  test('renders with correct text', () => {
    renderWithTheme(<Button>Click me</Button>);
    expect(screen.getByText('Click me')).toBeInTheDocument();
  });
  
  test('handles click events', () => {
    const handleClick = jest.fn();
    renderWithTheme(
      <Button onClick={handleClick}>Click me</Button>
    );
    
    fireEvent.click(screen.getByText('Click me'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
  
  test('shows loading state', () => {
    renderWithTheme(<Button loading>Loading</Button>);
    const button = screen.getByRole('button');
    
    expect(button).toBeDisabled();
    expect(screen.getByText('Loading')).toBeInTheDocument();
  });
  
  test('applies correct variant styles', () => {
    renderWithTheme(<Button variant="primary">Primary</Button>);
    const button = screen.getByRole('button');
    
    expect(button).toHaveClass('bg-primary-600');
  });
});
```

### Hook Testing

```typescript
// useTheme.test.ts
import { renderHook, act } from '@testing-library/react';
import { ThemeProvider, useTheme } from '../context/ThemeContext';

const wrapper = ({ children }: { children: React.ReactNode }) => (
  <ThemeProvider>{children}</ThemeProvider>
);

describe('useTheme Hook', () => {
  test('provides default theme', () => {
    const { result } = renderHook(() => useTheme(), { wrapper });
    
    expect(result.current.theme).toBe('light');
  });
  
  test('toggles theme correctly', () => {
    const { result } = renderHook(() => useTheme(), { wrapper });
    
    act(() => {
      result.current.toggleTheme();
    });
    
    expect(result.current.theme).toBe('dark');
  });
  
  test('sets specific theme', () => {
    const { result } = renderHook(() => useTheme(), { wrapper });
    
    act(() => {
      result.current.setTheme('dark');
    });
    
    expect(result.current.theme).toBe('dark');
  });
});
```

### Utility Function Testing

```typescript
// calculator.test.ts
import { CalculatorAPI } from '../api/calculator';

describe('Calculator API', () => {
  test('calculates municipal project cost correctly', () => {
    const params = {
      projectType: 'municipal' as const,
      capacity: 1000,
      location: 'Johannesburg',
      additionalFeatures: ['screening', 'clarification'],
      timeline: 'standard' as const
    };
    
    const result = CalculatorAPI.calculateCost(params);
    
    expect(result.baseCost).toBe(1500000);
    expect(result.totalCost).toBeGreaterThan(result.baseCost);
    expect(result.currency).toBe('ZAR');
  });
  
  test('applies location multiplier correctly', () => {
    const johannesburgParams = {
      projectType: 'municipal' as const,
      capacity: 1000,
      location: 'Johannesburg',
      additionalFeatures: [],
      timeline: 'standard' as const
    };
    
    const capeParams = { ...johannesburgParams, location: 'Cape Town' };
    
    const jhbResult = CalculatorAPI.calculateCost(johannesburgParams);
    const cptResult = CalculatorAPI.calculateCost(capeParams);
    
    expect(cptResult.installationCost).toBeGreaterThan(jhbResult.installationCost);
  });
});
```

## Integration Testing

### Form Integration Testing

```typescript
// ContactForm.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import ContactForm from '../components/sections/Contact';

const renderWithRouter = (component: React.ReactElement) => {
  return render(
    <BrowserRouter>
      {component}
    </BrowserRouter>
  );
};

describe('Contact Form Integration', () => {
  test('submits form successfully', async () => {
    renderWithRouter(<ContactForm />);
    
    // Fill out form
    fireEvent.change(screen.getByLabelText('Name'), {
      target: { value: 'John Doe' }
    });
    
    fireEvent.change(screen.getByLabelText('Email'), {
      target: { value: '<EMAIL>' }
    });
    
    fireEvent.change(screen.getByLabelText('Message'), {
      target: { value: 'Test message' }
    });
    
    // Submit form
    fireEvent.click(screen.getByText('Send Message'));
    
    // Wait for success message
    await waitFor(() => {
      expect(screen.getByText('Message sent successfully!')).toBeInTheDocument();
    });
  });
  
  test('shows validation errors', async () => {
    renderWithRouter(<ContactForm />);
    
    // Submit empty form
    fireEvent.click(screen.getByText('Send Message'));
    
    // Check for validation errors
    await waitFor(() => {
      expect(screen.getByText('Name is required')).toBeInTheDocument();
      expect(screen.getByText('Email is required')).toBeInTheDocument();
    });
  });
});
```

### Navigation Testing

```typescript
// Navigation.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import App from '../App';

describe('Navigation Integration', () => {
  test('navigates between pages correctly', () => {
    render(
      <BrowserRouter>
        <App />
      </BrowserRouter>
    );
    
    // Check home page is loaded
    expect(screen.getByText('Welcome to Pazogen')).toBeInTheDocument();
    
    // Navigate to calculator
    fireEvent.click(screen.getByText('Calculator'));
    
    // Check calculator page is loaded
    expect(screen.getByText('Cost Calculator')).toBeInTheDocument();
  });
  
  test('mobile menu works correctly', () => {
    // Mock mobile viewport
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 375,
    });
    
    render(
      <BrowserRouter>
        <App />
      </BrowserRouter>
    );
    
    // Open mobile menu
    fireEvent.click(screen.getByLabelText('Menu'));
    
    // Check menu items are visible
    expect(screen.getByText('Home')).toBeVisible();
    expect(screen.getByText('Projects')).toBeVisible();
  });
});
```

## End-to-End Testing

### Playwright Configuration

```typescript
// playwright.config.ts
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
    },
    {
      name: 'Mobile Safari',
      use: { ...devices['iPhone 12'] },
    },
  ],
  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:3000',
    reuseExistingServer: !process.env.CI,
  },
});
```

### E2E Test Examples

```typescript
// e2e/calculator.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Calculator Page', () => {
  test('calculates project cost correctly', async ({ page }) => {
    await page.goto('/calculator');
    
    // Fill out calculator form
    await page.selectOption('[data-testid="project-type"]', 'municipal');
    await page.fill('[data-testid="capacity"]', '1000');
    await page.selectOption('[data-testid="location"]', 'Johannesburg');
    
    // Check additional features
    await page.check('[data-testid="feature-screening"]');
    await page.check('[data-testid="feature-clarification"]');
    
    // Wait for calculation
    await expect(page.locator('[data-testid="total-cost"]')).toBeVisible();
    
    // Verify cost is calculated
    const totalCost = await page.textContent('[data-testid="total-cost"]');
    expect(totalCost).toContain('R');
  });
  
  test('generates PDF quote', async ({ page }) => {
    await page.goto('/calculator');
    
    // Fill out form (abbreviated)
    await page.selectOption('[data-testid="project-type"]', 'industrial');
    await page.fill('[data-testid="capacity"]', '500');
    
    // Start download
    const downloadPromise = page.waitForEvent('download');
    await page.click('[data-testid="generate-pdf"]');
    const download = await downloadPromise;
    
    // Verify download
    expect(download.suggestedFilename()).toContain('quote');
  });
});
```

### User Journey Testing

```typescript
// e2e/user-journey.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Complete User Journey', () => {
  test('user can request a quote from start to finish', async ({ page }) => {
    // Start at home page
    await page.goto('/');
    
    // Navigate to calculator
    await page.click('text=Get Quote');
    await expect(page).toHaveURL('/calculator');
    
    // Use calculator
    await page.selectOption('[data-testid="project-type"]', 'commercial');
    await page.fill('[data-testid="capacity"]', '750');
    await page.selectOption('[data-testid="location"]', 'Cape Town');
    
    // Proceed to quote request
    await page.click('text=Request Detailed Quote');
    await expect(page).toHaveURL('/get-a-quote');
    
    // Fill out quote form
    await page.fill('[data-testid="name"]', 'John Doe');
    await page.fill('[data-testid="email"]', '<EMAIL>');
    await page.fill('[data-testid="company"]', 'Test Company');
    await page.fill('[data-testid="phone"]', '+27123456789');
    
    // Submit quote request
    await page.click('text=Submit Quote Request');
    
    // Verify success
    await expect(page.locator('text=Quote request submitted')).toBeVisible();
  });
});
```

## Performance Testing

### Load Testing with Artillery

```yaml
# artillery.yml
config:
  target: 'http://localhost:3000'
  phases:
    - duration: 60
      arrivalRate: 10
    - duration: 120
      arrivalRate: 20
    - duration: 60
      arrivalRate: 5

scenarios:
  - name: "Homepage Load"
    weight: 50
    flow:
      - get:
          url: "/"
      - think: 5
      - get:
          url: "/projects"
  
  - name: "Calculator Usage"
    weight: 30
    flow:
      - get:
          url: "/calculator"
      - think: 10
      - post:
          url: "/api/calculate"
          json:
            projectType: "municipal"
            capacity: 1000
  
  - name: "Contact Form"
    weight: 20
    flow:
      - get:
          url: "/"
      - think: 5
      - post:
          url: "/api/contact"
          json:
            name: "Test User"
            email: "<EMAIL>"
```

### Bundle Size Testing

```typescript
// scripts/bundle-size-test.js
const fs = require('fs');
const path = require('path');

const BUNDLE_SIZE_LIMIT = 2 * 1024 * 1024; // 2MB

const checkBundleSize = () => {
  const distPath = path.join(__dirname, '../dist');
  const files = fs.readdirSync(distPath, { recursive: true });
  
  let totalSize = 0;
  
  files.forEach(file => {
    const filePath = path.join(distPath, file);
    const stats = fs.statSync(filePath);
    
    if (stats.isFile()) {
      totalSize += stats.size;
    }
  });
  
  console.log(`Total bundle size: ${(totalSize / 1024 / 1024).toFixed(2)}MB`);
  
  if (totalSize > BUNDLE_SIZE_LIMIT) {
    console.error('Bundle size exceeds limit!');
    process.exit(1);
  }
  
  console.log('Bundle size check passed ✅');
};

checkBundleSize();
```

## Accessibility Testing

### Automated A11y Testing

```typescript
// a11y.test.tsx
import { render } from '@testing-library/react';
import { axe, toHaveNoViolations } from 'jest-axe';
import App from '../App';

expect.extend(toHaveNoViolations);

describe('Accessibility Tests', () => {
  test('App has no accessibility violations', async () => {
    const { container } = render(<App />);
    const results = await axe(container);
    
    expect(results).toHaveNoViolations();
  });
  
  test('Calculator page is accessible', async () => {
    const { container } = render(<Calculator />);
    const results = await axe(container);
    
    expect(results).toHaveNoViolations();
  });
});
```

### Manual A11y Testing Checklist

```markdown
## Accessibility Testing Checklist

### Keyboard Navigation
- [ ] All interactive elements are keyboard accessible
- [ ] Tab order is logical
- [ ] Focus indicators are visible
- [ ] Escape key closes modals/dropdowns

### Screen Reader Testing
- [ ] All images have alt text
- [ ] Form labels are properly associated
- [ ] Headings are hierarchical
- [ ] ARIA labels are descriptive

### Color and Contrast
- [ ] Text meets WCAG contrast requirements
- [ ] Information isn't conveyed by color alone
- [ ] Focus indicators have sufficient contrast

### Responsive Design
- [ ] Content is accessible at 200% zoom
- [ ] Touch targets are at least 44px
- [ ] Content reflows properly
```

## Test Automation

### GitHub Actions CI

```yaml
# .github/workflows/test.yml
name: Test Suite

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run unit tests
      run: npm run test:coverage
    
    - name: Upload coverage
      uses: codecov/codecov-action@v3

  e2e-tests:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Install Playwright
      run: npx playwright install
    
    - name: Run E2E tests
      run: npm run test:e2e
    
    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: failure()
      with:
        name: playwright-report
        path: playwright-report/
```

---

> 📚 **Related Documentation**: [[development/README|Development Guide]] | [[components/README|Component Library]] | [[performance/README|Performance Guide]]
