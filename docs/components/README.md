# Component Library Documentation

> 🧩 **Comprehensive guide to <PERSON><PERSON>'s React component library**

## Component Hierarchy

```mermaid
graph TD
    A[App] --> B[Layout Components]
    A --> C[Page Components]
    A --> D[Section Components]
    A --> E[UI Components]
    
    B --> B1[Navbar]
    B --> B2[Footer]
    
    C --> C1[Calculator]
    C --> C2[QuoteRequest]
    
    D --> D1[Hero]
    D --> D2[About]
    D --> D3[Services]
    D --> D4[Team]
    D --> D5[Contact]
    D --> D6[Gallery]
    D --> D7[Projects]
    
    E --> E1[Button]
    E --> E2[Modal]
    E --> E3[Loading]
    E --> E4[WhatsAppButton]
    E --> E5[ScrollToTop]
    
    style A fill:#e3f2fd
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec
```

## Layout Components

### Navbar Component

**Location**: `src/components/layout/Navbar.tsx`

```typescript
interface NavbarProps {
  className?: string;
}

const Navbar: React.FC<NavbarProps> = ({ className }) => {
  // Implementation
};
```

**Features**:
- Responsive navigation with mobile menu
- Theme toggle integration
- Smooth scroll navigation
- Active link highlighting
- Mobile-optimized interactions

**Usage**:
```tsx
<Navbar />
```

### Footer Component

**Location**: `src/components/layout/Footer.tsx`

```mermaid
graph LR
    A[Footer] --> B[Company Info]
    A --> C[Quick Links]
    A --> D[Services]
    A --> E[Contact Info]
    
    B --> F[Logo]
    B --> G[Description]
    B --> H[Social Links]
    
    style A fill:#e1f5fe
```

## Section Components

### Hero Section

**Location**: `src/components/sections/Hero.tsx`

```typescript
interface HeroProps {
  title?: string;
  subtitle?: string;
  ctaText?: string;
  backgroundImage?: string;
}
```

**Key Features**:
- Typewriter animation effect
- Animated water wave background
- Responsive hero content
- Call-to-action buttons
- Parallax scrolling effects

### Services Section

**Location**: `src/components/sections/Services.tsx`

```mermaid
graph TB
    A[Services Section] --> B[Service Cards]
    B --> C[Screen Equipment]
    B --> D[Flow Control]
    B --> E[Clarifiers]
    
    C --> F[Image]
    C --> G[Description]
    C --> H[CTA Button]
    
    style A fill:#e8f5e8
    style B fill:#f3e5f5
```

## UI Components

### Button Component

**Location**: `src/components/ui/Button.tsx`

```typescript
interface ButtonProps {
  variant: 'primary' | 'secondary' | 'outline' | 'ghost';
  size: 'sm' | 'md' | 'lg';
  fullWidth?: boolean;
  disabled?: boolean;
  loading?: boolean;
  icon?: React.ReactNode;
  children: React.ReactNode;
  onClick?: () => void;
}
```

**Variants**:

| Variant | Description | Use Case |
|---------|-------------|----------|
| `primary` | Main action button | Primary CTAs |
| `secondary` | Secondary action | Alternative actions |
| `outline` | Outlined button | Subtle actions |
| `ghost` | Minimal button | Navigation, links |

### WhatsApp Button

**Location**: `src/components/ui/WhatsAppButton.tsx`

```typescript
interface WhatsAppButtonProps {
  phoneNumber?: string;
  message?: string;
  className?: string;
}
```

**Features**:
- Floating action button
- Animated pulse effect
- Mobile-optimized positioning
- Custom message support
- Accessibility compliant

### Advanced Calculator

**Location**: `src/components/ui/AdvancedCalculator.tsx`

```mermaid
graph TD
    A[Calculator] --> B[Input Form]
    A --> C[Cost Calculation]
    A --> D[Results Display]
    
    B --> E[Project Type]
    B --> F[Capacity]
    B --> G[Location]
    B --> H[Additional Features]
    
    C --> I[Base Cost]
    C --> J[Equipment Cost]
    C --> K[Installation Cost]
    C --> L[Total Cost]
    
    style A fill:#e3f2fd
    style C fill:#e8f5e8
```

## Page Components

### Calculator Page

**Location**: `src/pages/Calculator/Calculator.tsx`

```typescript
interface CalculatorPageProps {
  onCalculationChange?: (params: CalculatorParams, cost: CostBreakdown) => void;
}
```

**Features**:
- Real-time cost calculation
- PDF quote generation
- Mobile-responsive design
- Integration with quote system

### Quote Request Page

**Location**: `src/pages/QuoteRequest/QuoteRequest.tsx`

**Form Fields**:
- Personal information
- Project details
- Service requirements
- Timeline preferences
- Budget range

## Component Patterns

### 1. Compound Components

```typescript
// Modal compound component pattern
const Modal = ({ children, isOpen, onClose }) => {
  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div className="modal-overlay">
          {children}
        </motion.div>
      )}
    </AnimatePresence>
  );
};

Modal.Header = ({ children }) => (
  <div className="modal-header">{children}</div>
);

Modal.Body = ({ children }) => (
  <div className="modal-body">{children}</div>
);

Modal.Footer = ({ children }) => (
  <div className="modal-footer">{children}</div>
);
```

### 2. Render Props Pattern

```typescript
const DataFetcher = ({ render, url }) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    fetchData(url).then(setData).finally(() => setLoading(false));
  }, [url]);
  
  return render({ data, loading });
};

// Usage
<DataFetcher
  url="/api/projects"
  render={({ data, loading }) => (
    loading ? <Loading /> : <ProjectList projects={data} />
  )}
/>
```

### 3. Custom Hooks Integration

```typescript
const useAnimatedCounter = (end: number, duration: number = 2000) => {
  const [count, setCount] = useState(0);
  
  useEffect(() => {
    const startTime = Date.now();
    const timer = setInterval(() => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);
      setCount(Math.floor(progress * end));
      
      if (progress === 1) clearInterval(timer);
    }, 16);
    
    return () => clearInterval(timer);
  }, [end, duration]);
  
  return count;
};
```

## Animation Components

### Page Transitions

```typescript
const PageTransition: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    exit={{ opacity: 0, y: -20 }}
    transition={{ duration: 0.3 }}
  >
    {children}
  </motion.div>
);
```

### Scroll Animations

```typescript
const ScrollReveal: React.FC<ScrollRevealProps> = ({ 
  children, 
  direction = 'up',
  delay = 0 
}) => (
  <motion.div
    initial={{ 
      opacity: 0, 
      y: direction === 'up' ? 50 : -50 
    }}
    whileInView={{ opacity: 1, y: 0 }}
    viewport={{ once: true }}
    transition={{ duration: 0.6, delay }}
  >
    {children}
  </motion.div>
);
```

## Responsive Design Patterns

### Breakpoint System

```typescript
const breakpoints = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px'
};

const useBreakpoint = () => {
  const [breakpoint, setBreakpoint] = useState('sm');
  
  useEffect(() => {
    const updateBreakpoint = () => {
      const width = window.innerWidth;
      if (width >= 1536) setBreakpoint('2xl');
      else if (width >= 1280) setBreakpoint('xl');
      else if (width >= 1024) setBreakpoint('lg');
      else if (width >= 768) setBreakpoint('md');
      else setBreakpoint('sm');
    };
    
    updateBreakpoint();
    window.addEventListener('resize', updateBreakpoint);
    return () => window.removeEventListener('resize', updateBreakpoint);
  }, []);
  
  return breakpoint;
};
```

## Component Testing

### Unit Testing Example

```typescript
import { render, screen, fireEvent } from '@testing-library/react';
import { Button } from '../Button';

describe('Button Component', () => {
  test('renders with correct text', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByText('Click me')).toBeInTheDocument();
  });
  
  test('handles click events', () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    
    fireEvent.click(screen.getByText('Click me'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
  
  test('shows loading state', () => {
    render(<Button loading>Loading</Button>);
    expect(screen.getByRole('button')).toBeDisabled();
  });
});
```

## Component Performance

### Optimization Strategies

1. **React.memo** for expensive components
2. **useMemo** for expensive calculations
3. **useCallback** for stable function references
4. **Lazy loading** for heavy components

```typescript
const ExpensiveComponent = React.memo(({ data }) => {
  const processedData = useMemo(() => {
    return data.map(item => expensiveProcessing(item));
  }, [data]);
  
  return <div>{processedData}</div>;
});
```

---

> 📚 **Related Documentation**: [[architecture/README|Architecture]] | [[development/README|Development Guide]] | [[testing/README|Testing Guide]]
