# API Documentation

> 🔌 **Complete reference for external API integrations and data handling**

## API Overview

```mermaid
graph TB
    A[Pazogen App] --> B[EmailJS API]
    A --> C[Google Maps API]
    A --> D[Formspree API]
    A --> E[Internal APIs]
    
    B --> F[Contact Forms]
    B --> G[Quote Requests]
    
    C --> H[Location Services]
    C --> I[Map Display]
    
    D --> J[Form Submissions]
    D --> K[File Uploads]
    
    E --> L[Calculator Logic]
    E --> M[Data Processing]
    
    style A fill:#e3f2fd
    style B fill:#e8f5e8
    style C fill:#fff3e0
    style D fill:#f3e5f5
    style E fill:#fce4ec
```

## External API Integrations

### 1. EmailJS Integration

**Purpose**: Handle contact forms and email communications

**Configuration**:
```typescript
// EmailJS configuration
const emailConfig = {
  serviceId: process.env.VITE_EMAILJS_SERVICE_ID,
  templateId: process.env.VITE_EMAILJS_TEMPLATE_ID,
  publicKey: process.env.VITE_EMAILJS_PUBLIC_KEY
};
```

**Implementation**:
```typescript
import emailjs from '@emailjs/browser';

interface EmailData {
  from_name: string;
  from_email: string;
  subject: string;
  message: string;
  phone?: string;
  company?: string;
}

const sendEmail = async (data: EmailData): Promise<boolean> => {
  try {
    const response = await emailjs.send(
      emailConfig.serviceId,
      emailConfig.templateId,
      data,
      emailConfig.publicKey
    );
    
    return response.status === 200;
  } catch (error) {
    console.error('Email sending failed:', error);
    return false;
  }
};
```

**Email Templates**:

| Template | Purpose | Variables |
|----------|---------|-----------|
| Contact Form | General inquiries | `from_name`, `from_email`, `message` |
| Quote Request | Project quotes | `project_type`, `capacity`, `location` |
| Calculator Result | Cost estimates | `total_cost`, `breakdown`, `specifications` |

### 2. Google Maps API

**Purpose**: Location services and map display

**Configuration**:
```typescript
import { LoadScript, GoogleMap, Marker } from '@react-google-maps/api';

const mapConfig = {
  apiKey: process.env.VITE_GOOGLE_MAPS_API_KEY,
  libraries: ['places', 'geometry'] as const,
  center: { lat: -26.2041, lng: 28.0473 }, // Johannesburg
  zoom: 10
};
```

**Implementation**:
```typescript
const MapComponent: React.FC = () => {
  const [map, setMap] = useState<google.maps.Map | null>(null);
  
  const onLoad = useCallback((map: google.maps.Map) => {
    setMap(map);
  }, []);
  
  return (
    <LoadScript
      googleMapsApiKey={mapConfig.apiKey}
      libraries={mapConfig.libraries}
    >
      <GoogleMap
        mapContainerStyle={{ width: '100%', height: '400px' }}
        center={mapConfig.center}
        zoom={mapConfig.zoom}
        onLoad={onLoad}
      >
        <Marker position={mapConfig.center} />
      </GoogleMap>
    </LoadScript>
  );
};
```

**API Endpoints Used**:
- Maps JavaScript API
- Places API
- Geocoding API

### 3. Formspree Integration

**Purpose**: Form handling and file uploads

**Configuration**:
```typescript
import { useForm } from '@formspree/react';

const FormspreeForm: React.FC = () => {
  const [state, handleSubmit] = useForm(
    process.env.VITE_FORMSPREE_ENDPOINT
  );
  
  if (state.succeeded) {
    return <SuccessMessage />;
  }
  
  return (
    <form onSubmit={handleSubmit}>
      {/* Form fields */}
    </form>
  );
};
```

## Internal API Structure

### Calculator API

**Purpose**: Cost calculation logic and pricing algorithms

```typescript
interface CalculatorParams {
  projectType: 'municipal' | 'industrial' | 'commercial';
  capacity: number; // in m³/day
  location: string;
  additionalFeatures: string[];
  timeline: 'urgent' | 'standard' | 'flexible';
}

interface CostBreakdown {
  baseCost: number;
  equipmentCost: number;
  installationCost: number;
  maintenanceCost: number;
  totalCost: number;
  currency: string;
}

class CalculatorAPI {
  static calculateCost(params: CalculatorParams): CostBreakdown {
    const baseCost = this.getBaseCost(params.projectType, params.capacity);
    const equipmentCost = this.getEquipmentCost(params.additionalFeatures);
    const installationCost = this.getInstallationCost(params.location);
    const maintenanceCost = this.getMaintenanceCost(params.capacity);
    
    return {
      baseCost,
      equipmentCost,
      installationCost,
      maintenanceCost,
      totalCost: baseCost + equipmentCost + installationCost,
      currency: 'ZAR'
    };
  }
  
  private static getBaseCost(type: string, capacity: number): number {
    const rates = {
      municipal: 1500,
      industrial: 2000,
      commercial: 1200
    };
    
    return rates[type] * capacity;
  }
}
```

### Data Processing API

**Purpose**: Handle form data and business logic

```typescript
interface ProjectData {
  id: string;
  title: string;
  category: string;
  description: string;
  images: string[];
  services: string[];
  location: string;
  completionDate: string;
  clientName?: string;
}

class DataAPI {
  static async getProjects(filter?: string): Promise<ProjectData[]> {
    // Simulate API call
    const projects = await import('../data/projects.json');
    
    if (filter) {
      return projects.default.filter(p => 
        p.category.toLowerCase().includes(filter.toLowerCase())
      );
    }
    
    return projects.default;
  }
  
  static async getProject(id: string): Promise<ProjectData | null> {
    const projects = await this.getProjects();
    return projects.find(p => p.id === id) || null;
  }
}
```

## API Response Formats

### Success Response

```json
{
  "success": true,
  "data": {
    "id": "12345",
    "message": "Operation completed successfully"
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Error Response

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input parameters",
    "details": {
      "field": "email",
      "reason": "Invalid email format"
    }
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## Error Handling

### API Error Types

```typescript
enum APIErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  RATE_LIMIT_ERROR = 'RATE_LIMIT_ERROR',
  SERVER_ERROR = 'SERVER_ERROR'
}

class APIError extends Error {
  constructor(
    public type: APIErrorType,
    public message: string,
    public statusCode?: number,
    public details?: any
  ) {
    super(message);
    this.name = 'APIError';
  }
}
```

### Error Handling Strategy

```typescript
const handleAPIError = (error: APIError) => {
  switch (error.type) {
    case APIErrorType.NETWORK_ERROR:
      showNotification('Network error. Please check your connection.');
      break;
    
    case APIErrorType.VALIDATION_ERROR:
      showValidationErrors(error.details);
      break;
    
    case APIErrorType.RATE_LIMIT_ERROR:
      showNotification('Too many requests. Please try again later.');
      break;
    
    default:
      showNotification('An unexpected error occurred.');
  }
};
```

## Rate Limiting

### EmailJS Rate Limits

```typescript
class RateLimiter {
  private static requests: Map<string, number[]> = new Map();
  
  static canMakeRequest(endpoint: string, limit: number = 5): boolean {
    const now = Date.now();
    const windowMs = 60000; // 1 minute
    
    const requests = this.requests.get(endpoint) || [];
    const recentRequests = requests.filter(time => now - time < windowMs);
    
    if (recentRequests.length >= limit) {
      return false;
    }
    
    recentRequests.push(now);
    this.requests.set(endpoint, recentRequests);
    return true;
  }
}
```

## Caching Strategy

### API Response Caching

```typescript
class APICache {
  private static cache: Map<string, { data: any; timestamp: number }> = new Map();
  private static TTL = 5 * 60 * 1000; // 5 minutes
  
  static get(key: string): any | null {
    const cached = this.cache.get(key);
    
    if (!cached) return null;
    
    if (Date.now() - cached.timestamp > this.TTL) {
      this.cache.delete(key);
      return null;
    }
    
    return cached.data;
  }
  
  static set(key: string, data: any): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }
}
```

## API Testing

### Unit Tests

```typescript
import { CalculatorAPI } from '../api/calculator';

describe('Calculator API', () => {
  test('calculates municipal project cost correctly', () => {
    const params = {
      projectType: 'municipal' as const,
      capacity: 1000,
      location: 'Johannesburg',
      additionalFeatures: ['screening', 'clarification'],
      timeline: 'standard' as const
    };
    
    const result = CalculatorAPI.calculateCost(params);
    
    expect(result.baseCost).toBe(1500000); // 1500 * 1000
    expect(result.totalCost).toBeGreaterThan(result.baseCost);
    expect(result.currency).toBe('ZAR');
  });
});
```

### Integration Tests

```typescript
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import ContactForm from '../components/ContactForm';

test('contact form submits successfully', async () => {
  render(<ContactForm />);
  
  fireEvent.change(screen.getByLabelText('Name'), {
    target: { value: 'John Doe' }
  });
  
  fireEvent.change(screen.getByLabelText('Email'), {
    target: { value: '<EMAIL>' }
  });
  
  fireEvent.click(screen.getByText('Send Message'));
  
  await waitFor(() => {
    expect(screen.getByText('Message sent successfully!')).toBeInTheDocument();
  });
});
```

## API Security

### Environment Variables

```bash
# Production environment
VITE_EMAILJS_SERVICE_ID=service_xxxxxxx
VITE_EMAILJS_TEMPLATE_ID=template_xxxxxxx
VITE_EMAILJS_PUBLIC_KEY=xxxxxxxxxxxxxxx
VITE_GOOGLE_MAPS_API_KEY=AIzaSyXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
VITE_FORMSPREE_ENDPOINT=https://formspree.io/f/xxxxxxxx
```

### API Key Management

```typescript
const getAPIKey = (service: string): string => {
  const key = process.env[`VITE_${service.toUpperCase()}_API_KEY`];
  
  if (!key) {
    throw new Error(`Missing API key for ${service}`);
  }
  
  return key;
};
```

## Performance Optimization

### Request Optimization

```typescript
// Debounced API calls
const useDebouncedAPI = (apiCall: Function, delay: number = 300) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  
  const debouncedCall = useMemo(
    () => debounce(async (...args) => {
      setLoading(true);
      try {
        const result = await apiCall(...args);
        setData(result);
      } finally {
        setLoading(false);
      }
    }, delay),
    [apiCall, delay]
  );
  
  return { data, loading, call: debouncedCall };
};
```

---

> 📚 **Related Documentation**: [[development/README|Development Guide]] | [[components/README|Component Library]] | [[deployment/README|Deployment Guide]]
