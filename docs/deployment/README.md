# Deployment Guide

> 🚀 **Complete guide for building and deploying Pazogen to production**

## Deployment Overview

```mermaid
graph TB
    A[Source Code] --> B[Build Process]
    B --> C[Testing]
    C --> D[Bundle Optimization]
    D --> E[Asset Generation]
    E --> F[PWA Manifest]
    F --> G[Production Bundle]
    
    G --> H[Netlify]
    G --> I[Vercel]
    G --> J[AWS S3]
    G --> K[GitHub Pages]
    
    style A fill:#e3f2fd
    style G fill:#e8f5e8
    style H fill:#fff3e0
    style I fill:#f3e5f5
```

## Build Process

### Production Build

```bash
# Install dependencies
npm ci

# Run tests
npm run test

# Build for production
npm run build

# Preview build locally
npm run preview
```

### Build Configuration

```typescript
// vite.config.ts
export default defineConfig({
  plugins: [
    react(),
    VitePWA({
      registerType: 'autoUpdate',
      includeAssets: ['favicon.svg', 'robots.txt'],
      manifest: {
        name: 'Pazogen Water Treatment Solutions',
        short_name: '<PERSON><PERSON>',
        description: 'Complete turnkey solutions for wastewater and water treatment',
        theme_color: '#0ea5e9',
        background_color: '#ffffff',
        display: 'standalone',
        scope: '/',
        start_url: '/',
        icons: [
          {
            src: 'favicon.svg',
            sizes: '192x192',
            type: 'image/svg+xml'
          }
        ]
      }
    })
  ],
  build: {
    outDir: 'dist',
    sourcemap: false,
    minify: 'terser',
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          animations: ['framer-motion', 'gsap'],
          utils: ['lucide-react']
        }
      }
    }
  }
});
```

## Environment Configuration

### Environment Variables

```bash
# Production environment variables
VITE_EMAILJS_SERVICE_ID=prod_service_id
VITE_EMAILJS_TEMPLATE_ID=prod_template_id
VITE_EMAILJS_PUBLIC_KEY=prod_public_key
VITE_GOOGLE_MAPS_API_KEY=prod_maps_key
VITE_FORMSPREE_ENDPOINT=https://formspree.io/f/your-form-id
VITE_ANALYTICS_ID=GA_MEASUREMENT_ID
```

### Build Scripts

```json
{
  "scripts": {
    "build": "vite build",
    "build:analyze": "vite build && npx vite-bundle-analyzer dist/stats.html",
    "build:preview": "vite build && vite preview",
    "deploy:netlify": "npm run build && netlify deploy --prod --dir=dist",
    "deploy:vercel": "npm run build && vercel --prod"
  }
}
```

## Deployment Platforms

### 1. Netlify Deployment

```yaml
# netlify.toml
[build]
  publish = "dist"
  command = "npm run build"

[build.environment]
  NODE_VERSION = "18"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[context.production.environment]
  VITE_ENVIRONMENT = "production"

[context.deploy-preview.environment]
  VITE_ENVIRONMENT = "preview"
```

**Deployment Steps**:
```bash
# Install Netlify CLI
npm install -g netlify-cli

# Login to Netlify
netlify login

# Deploy to production
netlify deploy --prod --dir=dist
```

### 2. Vercel Deployment

```json
// vercel.json
{
  "version": 2,
  "builds": [
    {
      "src": "package.json",
      "use": "@vercel/static-build",
      "config": {
        "distDir": "dist"
      }
    }
  ],
  "routes": [
    {
      "src": "/(.*)",
      "dest": "/index.html"
    }
  ],
  "env": {
    "VITE_EMAILJS_SERVICE_ID": "@emailjs-service-id",
    "VITE_GOOGLE_MAPS_API_KEY": "@google-maps-key"
  }
}
```

**Deployment Steps**:
```bash
# Install Vercel CLI
npm install -g vercel

# Deploy to production
vercel --prod
```

### 3. AWS S3 + CloudFront

```yaml
# .github/workflows/deploy-aws.yml
name: Deploy to AWS S3

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Build
      run: npm run build
      env:
        VITE_EMAILJS_SERVICE_ID: ${{ secrets.EMAILJS_SERVICE_ID }}
        VITE_GOOGLE_MAPS_API_KEY: ${{ secrets.GOOGLE_MAPS_KEY }}
    
    - name: Deploy to S3
      uses: aws-actions/configure-aws-credentials@v2
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1
    
    - name: Sync to S3
      run: |
        aws s3 sync dist/ s3://${{ secrets.S3_BUCKET }} --delete
        aws cloudfront create-invalidation --distribution-id ${{ secrets.CLOUDFRONT_ID }} --paths "/*"
```

## Performance Optimization

### Bundle Analysis

```bash
# Analyze bundle size
npm run build:analyze

# Check bundle composition
npx vite-bundle-analyzer dist/stats.html
```

### Asset Optimization

```typescript
// Image optimization
const optimizeImages = () => {
  return {
    name: 'optimize-images',
    generateBundle(options, bundle) {
      // Image compression logic
    }
  };
};
```

### Caching Strategy

```typescript
// Service Worker caching
const cacheStrategy = {
  runtimeCaching: [
    {
      urlPattern: /^https:\/\/fonts\.googleapis\.com/,
      handler: 'StaleWhileRevalidate',
      options: {
        cacheName: 'google-fonts-stylesheets'
      }
    },
    {
      urlPattern: /\.(?:png|jpg|jpeg|svg|gif)$/,
      handler: 'CacheFirst',
      options: {
        cacheName: 'images',
        expiration: {
          maxEntries: 100,
          maxAgeSeconds: 30 * 24 * 60 * 60 // 30 days
        }
      }
    }
  ]
};
```

## Security Configuration

### Content Security Policy

```html
<!-- CSP Headers -->
<meta http-equiv="Content-Security-Policy" content="
  default-src 'self';
  script-src 'self' 'unsafe-inline' https://maps.googleapis.com;
  style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;
  font-src 'self' https://fonts.gstatic.com;
  img-src 'self' data: https:;
  connect-src 'self' https://api.emailjs.com https://formspree.io;
">
```

### HTTPS Configuration

```yaml
# Netlify _headers file
/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  Strict-Transport-Security: max-age=31536000; includeSubDomains
```

## Monitoring and Analytics

### Performance Monitoring

```typescript
// Performance tracking
const trackPerformance = () => {
  if ('performance' in window) {
    window.addEventListener('load', () => {
      const perfData = performance.getEntriesByType('navigation')[0];
      
      // Send to analytics
      gtag('event', 'page_load_time', {
        value: Math.round(perfData.loadEventEnd - perfData.fetchStart)
      });
    });
  }
};
```

### Error Tracking

```typescript
// Error boundary with reporting
class ErrorBoundary extends React.Component {
  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log to error tracking service
    if (process.env.NODE_ENV === 'production') {
      console.error('Production error:', error, errorInfo);
      // Send to Sentry, LogRocket, etc.
    }
  }
}
```

## CI/CD Pipeline

### GitHub Actions

```yaml
# .github/workflows/ci-cd.yml
name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run tests
      run: npm run test
    
    - name: Run linting
      run: npm run lint
    
    - name: Type check
      run: npm run type-check

  build:
    needs: test
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Build application
      run: npm run build
    
    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: build-files
        path: dist/

  deploy:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Download build artifacts
      uses: actions/download-artifact@v3
      with:
        name: build-files
        path: dist/
    
    - name: Deploy to production
      run: |
        # Deployment script
        echo "Deploying to production..."
```

## Health Checks

### Application Health

```typescript
// Health check endpoint
const healthCheck = {
  status: 'healthy',
  timestamp: new Date().toISOString(),
  version: process.env.npm_package_version,
  environment: process.env.NODE_ENV,
  services: {
    emailjs: 'operational',
    maps: 'operational',
    formspree: 'operational'
  }
};
```

### Monitoring Dashboard

```mermaid
graph TB
    A[Application] --> B[Performance Metrics]
    A --> C[Error Tracking]
    A --> D[User Analytics]
    
    B --> E[Core Web Vitals]
    B --> F[Load Times]
    B --> G[Bundle Size]
    
    C --> H[JavaScript Errors]
    C --> I[Network Failures]
    C --> J[User Reports]
    
    D --> K[Page Views]
    D --> L[User Interactions]
    D --> M[Conversion Rates]
    
    style A fill:#e3f2fd
    style B fill:#e8f5e8
    style C fill:#ffebee
    style D fill:#f3e5f5
```

## Rollback Strategy

### Deployment Rollback

```bash
# Netlify rollback
netlify sites:list
netlify api listSiteDeploys --data='{"site_id":"SITE_ID"}'
netlify api restoreSiteDeploy --data='{"site_id":"SITE_ID","deploy_id":"DEPLOY_ID"}'

# Vercel rollback
vercel list
vercel rollback [deployment-url]
```

### Database Migrations

```typescript
// Version management
const deploymentConfig = {
  version: '1.2.3',
  rollbackVersion: '1.2.2',
  migrations: [
    'add-calculator-feature',
    'update-contact-form'
  ],
  rollbackMigrations: [
    'remove-calculator-feature',
    'revert-contact-form'
  ]
};
```

---

> 📚 **Related Documentation**: [[development/README|Development Guide]] | [[performance/README|Performance Guide]] | [[architecture/README|Architecture]]
