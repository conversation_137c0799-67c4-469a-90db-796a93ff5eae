# Performance Optimization Guide

> ⚡ **Comprehensive guide to optimizing <PERSON><PERSON>'s performance and user experience**

## Performance Overview

```mermaid
graph TB
    A[Performance Optimization] --> B[Loading Performance]
    A --> C[Runtime Performance]
    A --> D[Network Performance]
    A --> E[User Experience]
    
    B --> F[Code Splitting]
    B --> G[Lazy Loading]
    B --> H[Bundle Optimization]
    
    C --> I[React Optimization]
    C --> J[Animation Performance]
    C --> K[Memory Management]
    
    D --> L[Caching Strategy]
    D --> M[Asset Optimization]
    D --> N[CDN Usage]
    
    E --> O[Core Web Vitals]
    E --> P[Accessibility]
    E --> Q[Mobile Performance]
    
    style A fill:#e3f2fd
    style B fill:#e8f5e8
    style C fill:#fff3e0
    style D fill:#f3e5f5
    style E fill:#fce4ec
```

## Core Web Vitals

### Current Performance Metrics

| Metric | Target | Current | Status |
|--------|--------|---------|--------|
| LCP (Largest Contentful Paint) | < 2.5s | 1.8s | ✅ Good |
| FID (First Input Delay) | < 100ms | 45ms | ✅ Good |
| CLS (Cumulative Layout Shift) | < 0.1 | 0.05 | ✅ Good |
| FCP (First Contentful Paint) | < 1.8s | 1.2s | ✅ Good |
| TTI (Time to Interactive) | < 3.8s | 2.9s | ✅ Good |

### Performance Monitoring

```typescript
// Performance monitoring implementation
class PerformanceMonitor {
  static measureWebVitals() {
    // Largest Contentful Paint
    new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      console.log('LCP:', lastEntry.startTime);
    }).observe({ entryTypes: ['largest-contentful-paint'] });
    
    // First Input Delay
    new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry) => {
        console.log('FID:', entry.processingStart - entry.startTime);
      });
    }).observe({ entryTypes: ['first-input'] });
    
    // Cumulative Layout Shift
    let clsValue = 0;
    new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (!entry.hadRecentInput) {
          clsValue += entry.value;
        }
      }
      console.log('CLS:', clsValue);
    }).observe({ entryTypes: ['layout-shift'] });
  }
}
```

## Code Splitting & Lazy Loading

### Route-Based Code Splitting

```typescript
// Lazy loading implementation
import { lazy, Suspense } from 'react';
import SuspenseLoader from '../components/ui/SuspenseLoader';

// Lazy load heavy components
const Gallery = lazy(() => import('../components/sections/Gallery'));
const Projects = lazy(() => import('../components/sections/Projects'));
const Calculator = lazy(() => import('../pages/Calculator/Calculator'));
const QuoteRequest = lazy(() => import('../pages/QuoteRequest/QuoteRequest'));

// Usage with Suspense
const LazyRoute: React.FC = () => (
  <Suspense fallback={<SuspenseLoader />}>
    <Calculator />
  </Suspense>
);
```

### Component-Based Code Splitting

```typescript
// Dynamic imports for heavy components
const loadHeavyComponent = async () => {
  const { default: HeavyComponent } = await import('../components/HeavyComponent');
  return HeavyComponent;
};

// Conditional loading
const ConditionalComponent: React.FC = ({ shouldLoad }) => {
  const [Component, setComponent] = useState<React.ComponentType | null>(null);
  
  useEffect(() => {
    if (shouldLoad && !Component) {
      loadHeavyComponent().then(setComponent);
    }
  }, [shouldLoad, Component]);
  
  return Component ? <Component /> : null;
};
```

## Bundle Optimization

### Webpack Bundle Analysis

```bash
# Analyze bundle size
npm run build:analyze

# Bundle composition
npx webpack-bundle-analyzer dist/static/js/*.js
```

### Manual Chunk Configuration

```typescript
// vite.config.ts
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          // Vendor libraries
          vendor: ['react', 'react-dom'],
          
          // Animation libraries
          animations: ['framer-motion', 'gsap'],
          
          // UI libraries
          ui: ['lucide-react'],
          
          // Form libraries
          forms: ['@emailjs/browser', '@formspree/react'],
          
          // Map libraries
          maps: ['@react-google-maps/api']
        }
      }
    }
  }
});
```

### Tree Shaking Optimization

```typescript
// Import only what you need
import { motion } from 'framer-motion'; // ✅ Good
import { Calendar, Mail, Phone } from 'lucide-react'; // ✅ Good

// Avoid importing entire libraries
import * as FramerMotion from 'framer-motion'; // ❌ Avoid
import * as LucideIcons from 'lucide-react'; // ❌ Avoid
```

## React Performance Optimization

### Component Memoization

```typescript
// React.memo for expensive components
const ExpensiveComponent = React.memo<Props>(({ data, onUpdate }) => {
  const processedData = useMemo(() => {
    return data.map(item => expensiveProcessing(item));
  }, [data]);
  
  const handleUpdate = useCallback((id: string) => {
    onUpdate(id);
  }, [onUpdate]);
  
  return (
    <div>
      {processedData.map(item => (
        <Item key={item.id} data={item} onUpdate={handleUpdate} />
      ))}
    </div>
  );
});
```

### Virtual Scrolling

```typescript
// Virtual scrolling for large lists
import { FixedSizeList as List } from 'react-window';

const VirtualizedList: React.FC<{ items: any[] }> = ({ items }) => {
  const Row = ({ index, style }) => (
    <div style={style}>
      <ListItem data={items[index]} />
    </div>
  );
  
  return (
    <List
      height={600}
      itemCount={items.length}
      itemSize={80}
      width="100%"
    >
      {Row}
    </List>
  );
};
```

### State Management Optimization

```typescript
// Optimize context usage
const ThemeContext = createContext<ThemeContextType | undefined>(undefined);
const UserContext = createContext<UserContextType | undefined>(undefined);

// Split contexts to avoid unnecessary re-renders
const App: React.FC = () => (
  <ThemeProvider>
    <UserProvider>
      <Router>
        <Routes />
      </Router>
    </UserProvider>
  </ThemeProvider>
);
```

## Animation Performance

### GPU-Accelerated Animations

```typescript
// Use transform properties for better performance
const optimizedAnimation = {
  initial: { 
    opacity: 0, 
    transform: 'translateY(20px) scale(0.95)' 
  },
  animate: { 
    opacity: 1, 
    transform: 'translateY(0px) scale(1)' 
  },
  transition: { 
    duration: 0.3,
    ease: 'easeOut'
  }
};

// Avoid animating layout properties
const slowAnimation = {
  initial: { height: 0, width: 0 }, // ❌ Causes layout thrashing
  animate: { height: 'auto', width: 'auto' }
};
```

### Animation Optimization Strategies

```typescript
// Use will-change for complex animations
const AnimatedComponent: React.FC = () => (
  <motion.div
    style={{ willChange: 'transform, opacity' }}
    animate={{ x: 100, opacity: 0.5 }}
    transition={{ duration: 0.3 }}
  />
);

// Reduce motion for accessibility
const useReducedMotion = () => {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);
  
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReducedMotion(mediaQuery.matches);
    
    const handler = (e: MediaQueryListEvent) => {
      setPrefersReducedMotion(e.matches);
    };
    
    mediaQuery.addEventListener('change', handler);
    return () => mediaQuery.removeEventListener('change', handler);
  }, []);
  
  return prefersReducedMotion;
};
```

## Image Optimization

### Responsive Images

```typescript
// Responsive image component
const ResponsiveImage: React.FC<ImageProps> = ({ 
  src, 
  alt, 
  sizes = '(max-width: 768px) 100vw, 50vw' 
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  
  return (
    <div className="relative overflow-hidden">
      {!isLoaded && (
        <div className="absolute inset-0 bg-gray-200 animate-pulse" />
      )}
      <img
        src={src}
        alt={alt}
        sizes={sizes}
        loading="lazy"
        decoding="async"
        onLoad={() => setIsLoaded(true)}
        className={`transition-opacity duration-300 ${
          isLoaded ? 'opacity-100' : 'opacity-0'
        }`}
      />
    </div>
  );
};
```

### Image Lazy Loading

```typescript
// Intersection Observer for lazy loading
const useLazyLoading = (ref: RefObject<HTMLElement>) => {
  const [isVisible, setIsVisible] = useState(false);
  
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );
    
    if (ref.current) {
      observer.observe(ref.current);
    }
    
    return () => observer.disconnect();
  }, [ref]);
  
  return isVisible;
};
```

## Caching Strategies

### Service Worker Caching

```typescript
// Service worker caching configuration
const cacheConfig = {
  runtimeCaching: [
    {
      urlPattern: /\.(?:png|jpg|jpeg|svg|gif)$/,
      handler: 'CacheFirst',
      options: {
        cacheName: 'images',
        expiration: {
          maxEntries: 100,
          maxAgeSeconds: 30 * 24 * 60 * 60 // 30 days
        }
      }
    },
    {
      urlPattern: /^https:\/\/fonts\.googleapis\.com/,
      handler: 'StaleWhileRevalidate',
      options: {
        cacheName: 'google-fonts-stylesheets'
      }
    },
    {
      urlPattern: /^https:\/\/api\./,
      handler: 'NetworkFirst',
      options: {
        cacheName: 'api-cache',
        networkTimeoutSeconds: 3
      }
    }
  ]
};
```

### Memory Management

```typescript
// Cleanup effects to prevent memory leaks
const useCleanupEffect = () => {
  useEffect(() => {
    const timer = setInterval(() => {
      // Periodic cleanup
    }, 60000);
    
    const handleResize = () => {
      // Handle resize
    };
    
    window.addEventListener('resize', handleResize);
    
    return () => {
      clearInterval(timer);
      window.removeEventListener('resize', handleResize);
    };
  }, []);
};
```

## Network Performance

### Request Optimization

```typescript
// Request batching
class RequestBatcher {
  private static batch: Array<{ url: string; resolve: Function }> = [];
  private static timeout: NodeJS.Timeout | null = null;
  
  static async request(url: string): Promise<any> {
    return new Promise((resolve) => {
      this.batch.push({ url, resolve });
      
      if (this.timeout) {
        clearTimeout(this.timeout);
      }
      
      this.timeout = setTimeout(() => {
        this.processBatch();
      }, 50); // Batch requests for 50ms
    });
  }
  
  private static async processBatch() {
    const currentBatch = [...this.batch];
    this.batch = [];
    
    // Process all requests in parallel
    const results = await Promise.all(
      currentBatch.map(({ url }) => fetch(url))
    );
    
    // Resolve all promises
    currentBatch.forEach(({ resolve }, index) => {
      resolve(results[index]);
    });
  }
}
```

### CDN Optimization

```typescript
// CDN configuration for static assets
const cdnConfig = {
  images: 'https://cdn.pazogen.com/images/',
  fonts: 'https://fonts.googleapis.com/',
  scripts: 'https://cdn.pazogen.com/js/',
  styles: 'https://cdn.pazogen.com/css/'
};

const getCDNUrl = (asset: string, type: keyof typeof cdnConfig): string => {
  return `${cdnConfig[type]}${asset}`;
};
```

## Performance Testing

### Lighthouse CI

```yaml
# .github/workflows/lighthouse.yml
name: Lighthouse CI

on:
  pull_request:
    branches: [main]

jobs:
  lighthouse:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Build
      run: npm run build
    
    - name: Run Lighthouse CI
      uses: treosh/lighthouse-ci-action@v9
      with:
        configPath: './lighthouserc.json'
        uploadArtifacts: true
```

### Performance Budget

```json
{
  "ci": {
    "collect": {
      "url": ["http://localhost:3000/"],
      "startServerCommand": "npm run preview"
    },
    "assert": {
      "assertions": {
        "categories:performance": ["warn", {"minScore": 0.9}],
        "categories:accessibility": ["error", {"minScore": 0.95}],
        "first-contentful-paint": ["warn", {"maxNumericValue": 2000}],
        "largest-contentful-paint": ["error", {"maxNumericValue": 2500}],
        "cumulative-layout-shift": ["error", {"maxNumericValue": 0.1}]
      }
    }
  }
}
```

## Mobile Performance

### Touch Optimization

```typescript
// Optimize touch interactions
const useTouchOptimization = () => {
  useEffect(() => {
    // Prevent 300ms click delay
    document.addEventListener('touchstart', () => {}, { passive: true });
    
    // Optimize scroll performance
    document.addEventListener('touchmove', (e) => {
      // Handle touch move
    }, { passive: true });
    
    return () => {
      // Cleanup listeners
    };
  }, []);
};
```

### Viewport Optimization

```typescript
// Responsive viewport handling
const useViewportOptimization = () => {
  const [viewport, setViewport] = useState({
    width: window.innerWidth,
    height: window.innerHeight,
    isMobile: window.innerWidth < 768
  });
  
  useEffect(() => {
    const handleResize = throttle(() => {
      setViewport({
        width: window.innerWidth,
        height: window.innerHeight,
        isMobile: window.innerWidth < 768
      });
    }, 100);
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  
  return viewport;
};
```

---

> 📚 **Related Documentation**: [[development/README|Development Guide]] | [[deployment/README|Deployment Guide]] | [[architecture/README|Architecture]]
