# Development Guide

> 💻 **Complete guide for developing and contributing to Pazogen**

## Development Environment Setup

### Prerequisites

```mermaid
graph LR
    A[Node.js 18+] --> B[npm/yarn]
    B --> C[Git]
    C --> D[VS Code]
    D --> E[Browser DevTools]
    
    style A fill:#e8f5e8
    style D fill:#e3f2fd
```

### Required Tools

| Tool | Version | Purpose |
|------|---------|---------|
| Node.js | 18+ | Runtime environment |
| npm | 8+ | Package manager |
| Git | 2.30+ | Version control |
| VS Code | Latest | Code editor |
| Chrome | Latest | Development browser |

### Installation Steps

```bash
# 1. Clone the repository
git clone https://github.com/Sainpse/pazogen.git
cd pazogen

# 2. Install dependencies
npm install

# 3. Set up environment variables
cp .env.example .env.local

# 4. Start development server
npm run dev
```

### Environment Variables

Create `.env.local` file:

```env
# EmailJS Configuration
VITE_EMAILJS_SERVICE_ID=your_service_id
VITE_EMAILJS_TEMPLATE_ID=your_template_id
VITE_EMAILJS_PUBLIC_KEY=your_public_key

# Google Maps API
VITE_GOOGLE_MAPS_API_KEY=your_maps_api_key

# Development Settings
VITE_DEV_MODE=true
VITE_API_BASE_URL=http://localhost:3000
```

## Development Workflow

```mermaid
gitGraph
    commit id: "Main Branch"
    branch feature/new-component
    checkout feature/new-component
    commit id: "Create Component"
    commit id: "Add Tests"
    commit id: "Update Docs"
    checkout main
    merge feature/new-component
    commit id: "Release"
```

### Branch Naming Convention

| Type | Format | Example |
|------|--------|---------|
| Feature | `feature/description` | `feature/calculator-component` |
| Bug Fix | `fix/description` | `fix/mobile-navigation` |
| Hotfix | `hotfix/description` | `hotfix/critical-bug` |
| Documentation | `docs/description` | `docs/api-reference` |

### Commit Message Format

```
type(scope): description

[optional body]

[optional footer]
```

**Types**:
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation
- `style`: Formatting
- `refactor`: Code restructuring
- `test`: Adding tests
- `chore`: Maintenance

**Examples**:
```bash
feat(calculator): add advanced cost calculation
fix(navbar): resolve mobile menu toggle issue
docs(components): update Button component documentation
```

## Code Standards

### TypeScript Configuration

```json
{
  "compilerOptions": {
    "target": "ES2020",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "allowJs": false,
    "skipLibCheck": true,
    "esModuleInterop": false,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "module": "ESNext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx"
  }
}
```

### ESLint Rules

```javascript
module.exports = {
  extends: [
    'eslint:recommended',
    '@typescript-eslint/recommended',
    'plugin:react/recommended',
    'plugin:react-hooks/recommended'
  ],
  rules: {
    'react/react-in-jsx-scope': 'off',
    '@typescript-eslint/no-unused-vars': 'error',
    'prefer-const': 'error',
    'no-var': 'error'
  }
};
```

### Component Structure

```typescript
// Component template
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

interface ComponentProps {
  // Props interface
  title: string;
  description?: string;
  className?: string;
}

const Component: React.FC<ComponentProps> = ({
  title,
  description,
  className = ''
}) => {
  // State and hooks
  const [isVisible, setIsVisible] = useState(false);
  
  // Effects
  useEffect(() => {
    // Side effects
  }, []);
  
  // Event handlers
  const handleClick = () => {
    // Handler logic
  };
  
  // Render
  return (
    <motion.div
      className={`component-base ${className}`}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
    >
      <h2>{title}</h2>
      {description && <p>{description}</p>}
    </motion.div>
  );
};

export default Component;
```

## Styling Guidelines

### Tailwind CSS Usage

```typescript
// ✅ Good - Semantic class grouping
const buttonClasses = `
  inline-flex items-center justify-center
  px-4 py-2 rounded-lg
  bg-primary-600 hover:bg-primary-700
  text-white font-semibold
  transition-colors duration-200
  focus:outline-none focus:ring-2 focus:ring-primary-500
`;

// ❌ Avoid - Long inline classes
<button className="inline-flex items-center justify-center px-4 py-2 rounded-lg bg-primary-600 hover:bg-primary-700 text-white font-semibold transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500">
```

### Responsive Design Patterns

```typescript
// Mobile-first approach
const responsiveClasses = `
  text-sm sm:text-base md:text-lg
  p-2 sm:p-4 md:p-6
  grid-cols-1 sm:grid-cols-2 lg:grid-cols-3
`;
```

## Animation Guidelines

### Framer Motion Best Practices

```typescript
// ✅ Good - Reusable animation variants
const fadeInUp = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: -20 },
  transition: { duration: 0.3 }
};

// ✅ Good - Performance-optimized animations
const optimizedAnimation = {
  initial: { opacity: 0, scale: 0.95 },
  animate: { opacity: 1, scale: 1 },
  transition: { 
    duration: 0.2,
    ease: "easeOut"
  }
};
```

### Animation Performance

```typescript
// Use transform properties for better performance
const performantAnimation = {
  initial: { opacity: 0, transform: "translateY(20px)" },
  animate: { opacity: 1, transform: "translateY(0px)" }
};

// Avoid animating layout properties
// ❌ Avoid
const slowAnimation = {
  initial: { height: 0, width: 0 },
  animate: { height: "auto", width: "auto" }
};
```

## Testing Strategy

### Unit Testing

```typescript
// Component test example
import { render, screen, fireEvent } from '@testing-library/react';
import { ThemeProvider } from '../context/ThemeContext';
import Button from '../components/ui/Button';

const renderWithTheme = (component: React.ReactElement) => {
  return render(
    <ThemeProvider>
      {component}
    </ThemeProvider>
  );
};

describe('Button Component', () => {
  test('renders correctly', () => {
    renderWithTheme(<Button>Test Button</Button>);
    expect(screen.getByText('Test Button')).toBeInTheDocument();
  });
  
  test('handles click events', () => {
    const handleClick = jest.fn();
    renderWithTheme(
      <Button onClick={handleClick}>Click me</Button>
    );
    
    fireEvent.click(screen.getByText('Click me'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
});
```

### Integration Testing

```typescript
// Page integration test
import { render, screen, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import Calculator from '../pages/Calculator/Calculator';

test('calculator page loads and functions correctly', async () => {
  render(
    <BrowserRouter>
      <Calculator />
    </BrowserRouter>
  );
  
  expect(screen.getByText('Cost Calculator')).toBeInTheDocument();
  
  // Test form interaction
  const capacityInput = screen.getByLabelText('Capacity');
  fireEvent.change(capacityInput, { target: { value: '1000' } });
  
  await waitFor(() => {
    expect(screen.getByText(/Total Cost/)).toBeInTheDocument();
  });
});
```

## Performance Optimization

### Code Splitting

```typescript
// Route-based code splitting
const Calculator = lazy(() => import('../pages/Calculator/Calculator'));
const Gallery = lazy(() => import('../components/sections/Gallery'));

// Component-based code splitting
const HeavyComponent = lazy(() => import('../components/HeavyComponent'));

// Usage with Suspense
<Suspense fallback={<Loading />}>
  <Calculator />
</Suspense>
```

### Bundle Analysis

```bash
# Analyze bundle size
npm run build
npm run analyze

# Performance profiling
npm run dev -- --profile
```

### Image Optimization

```typescript
// Lazy loading images
const LazyImage: React.FC<ImageProps> = ({ src, alt, className }) => {
  return (
    <img
      src={src}
      alt={alt}
      className={className}
      loading="lazy"
      decoding="async"
    />
  );
};
```

## Debugging Tools

### React Developer Tools

```typescript
// Component debugging
const DebugComponent: React.FC = () => {
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('Component mounted');
    }
  }, []);
  
  return <div>Debug Component</div>;
};
```

### Performance Monitoring

```typescript
// Performance measurement
const usePerformanceMonitor = () => {
  useEffect(() => {
    const observer = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        console.log(`${entry.name}: ${entry.duration}ms`);
      });
    });
    
    observer.observe({ entryTypes: ['measure'] });
    
    return () => observer.disconnect();
  }, []);
};
```

## Build Process

### Development Build

```bash
# Start development server
npm run dev

# Run with specific port
npm run dev -- --port 3001

# Run with host binding
npm run dev -- --host 0.0.0.0
```

### Production Build

```bash
# Build for production
npm run build

# Preview production build
npm run preview

# Analyze bundle
npm run build:analyze
```

### Build Configuration

```typescript
// vite.config.ts
export default defineConfig({
  plugins: [
    react(),
    VitePWA({
      registerType: 'autoUpdate',
      workbox: {
        globPatterns: ['**/*.{js,css,html,ico,png,svg}']
      }
    })
  ],
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          animations: ['framer-motion', 'gsap']
        }
      }
    }
  }
});
```

---

> 📚 **Related Documentation**: [[components/README|Component Library]] | [[testing/README|Testing Guide]] | [[deployment/README|Deployment Guide]]
