import { useState, useEffect } from 'react';

interface ViewportSize {
  width: number;
  height: number;
}

export const useViewport = (): ViewportSize => {
  const [viewport, setViewport] = useState<ViewportSize>({
    width: typeof window !== 'undefined' ? window.innerWidth : 0,
    height: typeof window !== 'undefined' ? window.innerHeight : 0,
  });

  useEffect(() => {
    const handleResize = () => {
      setViewport({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    // Add event listener
    window.addEventListener('resize', handleResize);
    
    // Call handler right away so state gets updated with initial window size
    handleResize();

    // Remove event listener on cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return viewport;
};

export const useIsMobile = (breakpoint: number = 768): boolean => {
  const { width } = useViewport();
  return width < breakpoint;
};

export const useIsTablet = (minWidth: number = 768, maxWidth: number = 1024): boolean => {
  const { width } = useViewport();
  return width >= minWidth && width <= maxWidth;
};

export const useIsDesktop = (breakpoint: number = 1024): boolean => {
  const { width } = useViewport();
  return width > breakpoint;
};
