@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base styles for better accessibility and performance */
@layer base {
  html {
    scroll-behavior: smooth;
    scroll-padding-top: 4rem;
  }

  body {
    font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Focus styles for better accessibility */
  *:focus-visible {
    outline: 2px solid theme('colors.primary.500');
    outline-offset: 2px;
    border-radius: 4px;
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .bg-gradient-to-r,
    .bg-gradient-to-br,
    .bg-gradient-to-l {
      background: theme('colors.gray.900');
    }

    .dark .bg-gradient-to-r,
    .dark .bg-gradient-to-br,
    .dark .bg-gradient-to-l {
      background: theme('colors.white');
    }
  }
}

@keyframes wave1 {
  0% {
    transform: translateX(0) translateZ(0);
  }
  50% {
    transform: translateX(-25%) translateZ(0);
  }
  100% {
    transform: translateX(-50%) translateZ(0);
  }
}

@keyframes wave2 {
  0% {
    transform: translateX(0) translateZ(0);
  }
  50% {
    transform: translateX(25%) translateZ(0);
  }
  100% {
    transform: translateX(50%) translateZ(0);
  }
}

@keyframes wave3 {
  0% {
    transform: translateX(-50%) translateZ(0);
  }
  50% {
    transform: translateX(0) translateZ(0);
  }
  100% {
    transform: translateX(-50%) translateZ(0);
  }
}

.wave1 {
  animation: wave1 12s linear infinite;
  will-change: transform;
}

.wave2 {
  animation: wave2 18s linear infinite;
  will-change: transform;
}

.wave3 {
  animation: wave3 24s linear infinite;
  will-change: transform;
}

/* Custom utility classes */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Glass morphism effect */
  .glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .glass-dark {
    background: rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Enhanced gradient text */
  .gradient-text {
    background: linear-gradient(135deg, theme('colors.primary.600'), theme('colors.blue.500'), theme('colors.cyan.500'));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Improved shadow utilities */
  .shadow-glow {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }

  .shadow-glow-lg {
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.4);
  }

  /* Performance optimized animations */
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-pulse-slow {
    animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  /* Ensure fixed elements stay in position */
  .fixed-bottom-right {
    position: fixed !important;
    bottom: calc(1rem + env(safe-area-inset-bottom)) !important;
    right: calc(1rem + env(safe-area-inset-right)) !important;
    z-index: 60 !important;
  }

  @media (min-width: 640px) {
    .fixed-bottom-right {
      bottom: calc(1.5rem + env(safe-area-inset-bottom)) !important;
      right: calc(1.5rem + env(safe-area-inset-right)) !important;
    }
  }

  @media (min-width: 768px) {
    .fixed-bottom-right {
      bottom: calc(2rem + env(safe-area-inset-bottom)) !important;
      right: calc(2rem + env(safe-area-inset-right)) !important;
    }
  }

  /* Prevent layout shifts */
  .floating-button {
    will-change: transform;
    transform: translateZ(0);
    backface-visibility: hidden;
  }
}

/* Additional keyframes for enhanced animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-10px) rotate(1deg);
  }
}