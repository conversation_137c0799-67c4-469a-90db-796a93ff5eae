import { useEffect } from 'react';

interface PerformanceMonitorProps {
  onLoadComplete?: (metrics: {
    loadTime: number;
    domContentLoaded: number;
    firstContentfulPaint?: number;
  }) => void;
}

const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({ onLoadComplete }) => {
  useEffect(() => {
    const measurePerformance = () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      
      if (navigation) {
        const metrics = {
          loadTime: navigation.loadEventEnd - navigation.loadEventStart,
          domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
        };

        // Try to get First Contentful Paint if available
        const paintEntries = performance.getEntriesByType('paint');
        const fcp = paintEntries.find(entry => entry.name === 'first-contentful-paint');
        
        if (fcp) {
          (metrics as any).firstContentfulPaint = fcp.startTime;
        }

        onLoadComplete?.(metrics);

        // Log performance metrics in development
        if (process.env.NODE_ENV === 'development') {
          console.group('🚀 Performance Metrics');
          console.log('Load Time:', `${metrics.loadTime.toFixed(2)}ms`);
          console.log('DOM Content Loaded:', `${metrics.domContentLoaded.toFixed(2)}ms`);
          if ((metrics as any).firstContentfulPaint) {
            console.log('First Contentful Paint:', `${(metrics as any).firstContentfulPaint.toFixed(2)}ms`);
          }
          console.groupEnd();
        }
      }
    };

    // Wait for the page to fully load
    if (document.readyState === 'complete') {
      measurePerformance();
    } else {
      window.addEventListener('load', measurePerformance);
      return () => window.removeEventListener('load', measurePerformance);
    }
  }, [onLoadComplete]);

  return null; // This component doesn't render anything
};

export default PerformanceMonitor;
