import React, { useEffect, useRef, useMemo } from 'react';
import { gsap } from 'gsap';

interface WavePoint {
  x: number;
  y: number;
  originY: number;
  speed: number;
  range: number;
  delay: number;
}

interface LoadingProps {
  isLoading: boolean;
}

const Loading: React.FC<LoadingProps> = ({ isLoading }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const waveRef = useRef<SVGSVGElement>(null);
  const animationRef = useRef<gsap.core.Timeline[]>([]);
  
  const { points, bubbles } = useMemo(() => {
    const pointCount = 15;
    const bubbleCount = 8;
    const points: WavePoint[] = [];
    const bubbles: { x: number; y: number; size: number; speed: number; delay: number }[] = [];
    
    // Create wave points
    for (let i = 0; i <= pointCount; i++) {
      points.push({
        x: (i / pointCount) * 200,
        y: 0,
        originY: 0,
        speed: 0.2 + Math.random() * 0.3,
        range: 6 + Math.random() * 4,
        delay: i * 0.04
      });
    }
    
    // Create bubbles
    for (let i = 0; i < bubbleCount; i++) {
      bubbles.push({
        x: Math.random() * 180 + 10,
        y: 40 + Math.random() * 20,
        size: 2 + Math.random() * 4,
        speed: 0.5 + Math.random() * 0.8,
        delay: Math.random() * 2
      });
    }
    
    return { points, bubbles };
  }, []);

  useEffect(() => {
    if (!isLoading || !waveRef.current) return;
    
    const wavePath = waveRef.current.querySelector('.wave-path');
    const bubbleElements = Array.from(waveRef.current.querySelectorAll('.bubble'));
    if (!wavePath) return;

    // Clear any existing animations
    animationRef.current.forEach(anim => anim?.kill());
    animationRef.current = [];
    
    // Wave animation
    const waveTl = gsap.timeline({ repeat: -1, yoyo: true });
    
    points.forEach((point, i) => {
      const tween = gsap.to(point, {
        y: point.range,
        duration: 1.5,
        ease: 'sine.inOut',
        delay: point.delay,
        onUpdate: updateWave,
        onUpdateParams: [wavePath]
      });
      animationRef.current.push(tween);
    });
    
    // Bubbles animation
    bubbleElements.forEach((bubble, i) => {
      const bubbleTl = gsap.timeline({ repeat: -1, delay: bubbles[i].delay });
      
      bubbleTl.to(bubble, {
        y: -50,
        x: `+=${(Math.random() - 0.5) * 20}`,
        opacity: 0,
        duration: bubbles[i].speed * 5,
        ease: 'power1.inOut',
        onComplete: () => {
          gsap.set(bubble, {
            y: 40 + Math.random() * 20,
            x: Math.random() * 180 + 10,
            opacity: 0.6
          });
        }
      });
      
      animationRef.current.push(bubbleTl);
    });

    function updateWave(path: SVGPathElement) {
      let d = 'M 0 40 ';
      
      points.forEach((point, i) => {
        const y = 30 + point.y;
        if (i === 0) {
          d += `L ${point.x} ${y} `;
        } else {
          const prevPoint = points[i - 1];
          const cpx = prevPoint.x + (point.x - prevPoint.x) / 2;
          d += `Q ${cpx} ${prevPoint.y + 30 + (point.y - prevPoint.y) / 2}, ${point.x} ${y} `;
        }
      });
      
      d += 'L 200 40 L 0 40 Z';
      path.setAttribute('d', d);
    }

    return () => {
      animationRef.current.forEach(anim => anim?.kill());
    };
  }, [isLoading, points]);

  if (!isLoading) return null;

  return (
    <div
      ref={containerRef}
      className="fixed inset-0 z-50 flex items-center justify-center bg-gradient-to-br from-white via-blue-50/30 to-cyan-50/30 dark:from-gray-900 dark:via-blue-950/30 dark:to-cyan-950/30 transition-all duration-500"
      role="dialog"
      aria-label="Loading content"
      aria-live="polite"
    >
      <div className="p-12 rounded-3xl flex flex-col items-center bg-white/95 dark:bg-gray-800/95 backdrop-blur-xl border border-blue-100/80 dark:border-blue-900/80 shadow-2xl max-w-sm w-full mx-4">
        {/* Logo or Brand */}
        <div className="mb-6">
          <img
            src="/logoPazogen.png"
            alt="Pazogen Logo"
            className="h-16 w-auto object-contain opacity-90"
          />
        </div>

        <div className="relative w-48 h-24 mb-8">
          <svg
            ref={waveRef}
            viewBox="0 0 200 60"
            className="w-full h-full"
            preserveAspectRatio="none"
            aria-hidden="true"
          >
            <defs>
              <linearGradient id="wave-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#60a5fa" />
                <stop offset="50%" stopColor="#3b82f6" />
                <stop offset="100%" stopColor="#2563eb" />
              </linearGradient>
              <linearGradient id="wave-gradient-dark" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#7dd3fc" />
                <stop offset="50%" stopColor="#38bdf8" />
                <stop offset="100%" stopColor="#0ea5e9" />
              </linearGradient>
              <filter id="wave-blur" x="-20%" y="-20%" width="140%" height="140%">
                <feGaussianBlur in="SourceGraphic" stdDeviation="1" />
              </filter>
            </defs>

            {/* Main wave */}
            <path
              className="wave-path"
              fill="url(#wave-gradient)"
              opacity="0.8"
              filter="url(#wave-blur)"
            />

            {/* Secondary reflection */}
            <path
              className="wave-path"
              fill="url(#wave-gradient)"
              opacity="0.4"
              transform="translate(0, 5)"
              filter="url(#wave-blur)"
            />

            {/* Bubbles */}
            {bubbles.map((bubble, i) => (
              <circle
                key={i}
                className="bubble fill-cyan-400/70 dark:fill-cyan-300/70"
                cx={bubble.x}
                cy={bubble.y}
                r={bubble.size}
              />
            ))}
          </svg>
        </div>

        <div className="text-center">
          <h2 className="text-blue-700 dark:text-blue-200 font-semibold text-xl mb-2">
            Loading Experience
          </h2>
          <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
            Preparing your water solutions...
          </p>
          <div className="flex justify-center space-x-1.5" aria-hidden="true">
            {[0, 1, 2].map((i) => (
              <div
                key={i}
                className="w-2 h-2 bg-gradient-to-r from-blue-500 to-cyan-500 dark:from-blue-400 dark:to-cyan-400 rounded-full"
                style={{
                  animation: `loadingPulse 1.8s ease-in-out ${i * 0.3}s infinite`,
                }}
              />
            ))}
          </div>
        </div>
      </div>

      <style jsx global>{`
        @keyframes loadingPulse {
          0%, 100% {
            opacity: 0.4;
            transform: scale(0.8) translateY(0);
          }
          50% {
            opacity: 1;
            transform: scale(1.2) translateY(-4px);
          }
        }
      `}</style>
    </div>
  );
};

export default Loading;
