import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Calculator,
  TrendingUp,
  <PERSON><PERSON>hart3,
  <PERSON><PERSON><PERSON>,
  Save,
  Share2,
  Droplets,
  Settings,
  Shield,
  Zap,
  Filter,
  Factory,
  Wrench,
  DollarSign,
  Clock,
  Users,
  MapPin,
  Info
} from 'lucide-react';
import { formatZAR } from '../../utils/currency';

// Types for calculator
export interface CalculatorParams {
  serviceType: string;
  flowRate: number; // L/min or m³/day
  treatmentLevel: string;
  capacity: number; // people served or volume
  location: string;
  urgency: string;
  additionalFeatures: string[];
  maintenanceLevel: string;
}

export interface CostBreakdown {
  equipment: number;
  installation: number;
  maintenance: number;
  permits: number;
  total: number;
}

interface AdvancedCalculatorProps {
  onCalculationChange?: (params: CalculatorParams, cost: CostBreakdown) => void;
  initialParams?: Partial<CalculatorParams>;
  className?: string;
}

const AdvancedCalculator: React.FC<AdvancedCalculatorProps> = ({
  onCalculationChange,
  initialParams = {},
  className = ''
}) => {
  // Calculator state
  const [params, setParams] = useState<CalculatorParams>({
    serviceType: 'water-treatment',
    flowRate: 1000,
    treatmentLevel: 'standard',
    capacity: 100,
    location: 'urban',
    urgency: 'standard',
    additionalFeatures: [],
    maintenanceLevel: 'standard',
    ...initialParams
  });

  const [costBreakdown, setCostBreakdown] = useState<CostBreakdown>({
    equipment: 0,
    installation: 0,
    maintenance: 0,
    permits: 0,
    total: 0
  });

  const [activeTab, setActiveTab] = useState<'calculator' | 'breakdown' | 'comparison'>('calculator');
  const [showTooltip, setShowTooltip] = useState<string | null>(null);

  // Service options with detailed parameters (Prices in ZAR with estimation markup + 200% increase)
  const serviceOptions = [
    {
      id: 'water-treatment',
      name: 'Water Treatment',
      icon: <Droplets className="w-5 h-5" />,
      basePrice: 285000, // R285,000 (increased by 200% from R95,000)
      pricePerUnit: 2850, // R2,850 per L/min capacity (increased by 200%)
      description: 'Complete water purification systems'
    },
    {
      id: 'wastewater-treatment',
      name: 'Wastewater Treatment',
      icon: <Factory className="w-5 h-5" />,
      basePrice: 456000, // R456,000 (increased by 200% from R152,000)
      pricePerUnit: 4275, // R4,275 per L/min capacity (increased by 200%)
      description: 'Industrial and municipal wastewater solutions'
    },
    {
      id: 'screen-equipment',
      name: 'Screen Equipment',
      icon: <Filter className="w-5 h-5" />,
      basePrice: 171000, // R171,000 (increased by 200% from R57,000)
      pricePerUnit: 1710, // R1,710 per L/min capacity (increased by 200%)
      description: 'Mechanical and manual screening systems'
    },
    {
      id: 'flow-control',
      name: 'Flow Control',
      icon: <Settings className="w-5 h-5" />,
      basePrice: 142500, // R142,500 (increased by 200% from R47,500)
      pricePerUnit: 1425, // R1,425 per L/min capacity (increased by 200%)
      description: 'Gates, weirs, and flow management'
    },
    {
      id: 'clarifiers',
      name: 'Clarifiers',
      icon: <Shield className="w-5 h-5" />,
      basePrice: 342000, // R342,000 (increased by 200% from R114,000)
      pricePerUnit: 3420, // R3,420 per L/min capacity (increased by 200%)
      description: 'Settling tanks and clarification systems'
    },
    {
      id: 'maintenance',
      name: 'Maintenance Services',
      icon: <Wrench className="w-5 h-5" />,
      basePrice: 85500, // R85,500 (increased by 200% from R28,500)
      pricePerUnit: 855, // R855 per L/min capacity (increased by 200%)
      description: 'Ongoing maintenance and support'
    }
  ];

  // Treatment level options
  const treatmentLevels = [
    { id: 'basic', name: 'Basic Treatment', multiplier: 0.8, description: 'Standard filtration and basic treatment' },
    { id: 'standard', name: 'Standard Treatment', multiplier: 1.0, description: 'Comprehensive treatment with quality monitoring' },
    { id: 'advanced', name: 'Advanced Treatment', multiplier: 1.5, description: 'High-tech treatment with automation' },
    { id: 'premium', name: 'Premium Treatment', multiplier: 2.0, description: 'State-of-the-art treatment with full automation' }
  ];

  // Location multipliers
  const locationMultipliers = {
    'urban': 1.0,
    'suburban': 1.1,
    'rural': 1.3,
    'remote': 1.6
  };

  // Urgency multipliers
  const urgencyMultipliers = {
    'standard': 1.0,
    'urgent': 1.3,
    'emergency': 1.8
  };

  // Additional features (Prices in ZAR with estimation markup + 200% increase)
  const additionalFeatures = [
    { id: 'automation', name: 'Full Automation', cost: 285000, description: 'Automated monitoring and control' }, // R285,000 (increased by 200%)
    { id: 'remote-monitoring', name: 'Remote Monitoring', cost: 114000, description: '24/7 remote system monitoring' }, // R114,000 (increased by 200%)
    { id: 'backup-power', name: 'Backup Power', cost: 171000, description: 'Emergency power backup system' }, // R171,000 (increased by 200%)
    { id: 'chemical-dosing', name: 'Chemical Dosing', cost: 85500, description: 'Automated chemical dosing system' }, // R85,500 (increased by 200%)
    { id: 'ph-control', name: 'pH Control', cost: 68400, description: 'Automated pH monitoring and control' }, // R68,400 (increased by 200%)
    { id: 'turbidity-monitoring', name: 'Turbidity Monitoring', cost: 45600, description: 'Real-time water quality monitoring' } // R45,600 (increased by 200%)
  ];

  // Calculate costs
  const calculateCosts = useCallback(() => {
    const selectedService = serviceOptions.find(s => s.id === params.serviceType);
    if (!selectedService) return;

    const treatmentLevel = treatmentLevels.find(t => t.id === params.treatmentLevel);
    if (!treatmentLevel) return;

    // Base equipment cost
    let equipmentCost = selectedService.basePrice + (selectedService.pricePerUnit * params.flowRate / 100);
    
    // Apply treatment level multiplier
    equipmentCost *= treatmentLevel.multiplier;
    
    // Apply location multiplier
    equipmentCost *= locationMultipliers[params.location as keyof typeof locationMultipliers] || 1.0;
    
    // Apply urgency multiplier
    equipmentCost *= urgencyMultipliers[params.urgency as keyof typeof urgencyMultipliers] || 1.0;

    // Add additional features
    const featuresCost = params.additionalFeatures.reduce((total, featureId) => {
      const feature = additionalFeatures.find(f => f.id === featureId);
      return total + (feature?.cost || 0);
    }, 0);

    equipmentCost += featuresCost;

    // Calculate other costs
    const installationCost = equipmentCost * 0.3; // 30% of equipment cost
    const permitsCost = equipmentCost * 0.05; // 5% of equipment cost
    
    // Maintenance cost based on level
    const maintenanceMultipliers = { basic: 0.1, standard: 0.15, premium: 0.25 };
    const maintenanceCost = equipmentCost * (maintenanceMultipliers[params.maintenanceLevel as keyof typeof maintenanceMultipliers] || 0.15);

    const breakdown: CostBreakdown = {
      equipment: Math.round(equipmentCost),
      installation: Math.round(installationCost),
      maintenance: Math.round(maintenanceCost),
      permits: Math.round(permitsCost),
      total: Math.round(equipmentCost + installationCost + maintenanceCost + permitsCost)
    };

    setCostBreakdown(breakdown);
    onCalculationChange?.(params, breakdown);
  }, [params, onCalculationChange]);

  // Update costs when parameters change
  useEffect(() => {
    calculateCosts();
  }, [calculateCosts]);

  // Handle parameter changes
  const updateParam = (key: keyof CalculatorParams, value: any) => {
    setParams(prev => ({ ...prev, [key]: value }));
  };

  // Handle feature toggle
  const toggleFeature = (featureId: string) => {
    setParams(prev => ({
      ...prev,
      additionalFeatures: prev.additionalFeatures.includes(featureId)
        ? prev.additionalFeatures.filter(id => id !== featureId)
        : [...prev.additionalFeatures, featureId]
    }));
  };

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 overflow-hidden ${className}`}>
      {/* Header */}
      <div className="bg-gradient-to-r from-primary-600 to-primary-700 p-4 sm:p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Calculator className="w-6 h-6 text-white" />
            <h3 className="text-lg sm:text-xl font-bold text-white">Advanced Quote Calculator</h3>
          </div>
          <div className="flex items-center space-x-2">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="p-2 bg-white/20 rounded-lg text-white hover:bg-white/30 transition-colors"
              title="Save Quote"
            >
              <Save className="w-4 h-4" />
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="p-2 bg-white/20 rounded-lg text-white hover:bg-white/30 transition-colors"
              title="Share Quote"
            >
              <Share2 className="w-4 h-4" />
            </motion.button>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="flex space-x-8 px-4 sm:px-6">
          {[
            { id: 'calculator', name: 'Calculator', icon: Calculator },
            { id: 'breakdown', name: 'Cost Breakdown', icon: BarChart3 },
            { id: 'comparison', name: 'Compare Options', icon: PieChart }
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center space-x-2 py-4 border-b-2 font-medium text-sm transition-colors ${
                activeTab === tab.id
                  ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              <tab.icon className="w-4 h-4" />
              <span className="hidden sm:inline">{tab.name}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="p-4 sm:p-6">
        <AnimatePresence mode="wait">
          {activeTab === 'calculator' && (
            <motion.div
              key="calculator"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-6"
            >
              {/* Service Type Selection */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                  Service Type
                </label>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                  {serviceOptions.map(service => (
                    <motion.button
                      key={service.id}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => updateParam('serviceType', service.id)}
                      className={`p-4 rounded-xl border-2 transition-all text-left ${
                        params.serviceType === service.id
                          ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                          : 'border-gray-200 dark:border-gray-600 hover:border-primary-300'
                      }`}
                    >
                      <div className="flex items-center space-x-3 mb-2">
                        {service.icon}
                        <span className="font-medium text-sm">{service.name}</span>
                      </div>
                      <p className="text-xs text-gray-600 dark:text-gray-400">{service.description}</p>
                    </motion.button>
                  ))}
                </div>
              </div>

              {/* Flow Rate Slider */}
              <div>
                <div className="flex items-center justify-between mb-3">
                  <label className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                    Flow Rate (L/min)
                  </label>
                  <span className="text-sm font-medium text-primary-600 dark:text-primary-400">
                    {params.flowRate.toLocaleString()} L/min
                  </span>
                </div>
                <input
                  type="range"
                  min="100"
                  max="10000"
                  step="100"
                  value={params.flowRate}
                  onChange={(e) => updateParam('flowRate', parseInt(e.target.value))}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700 slider"
                  style={{
                    background: `linear-gradient(to right, #3b82f6 0%, #3b82f6 ${((params.flowRate - 100) / (10000 - 100)) * 100}%, #e5e7eb ${((params.flowRate - 100) / (10000 - 100)) * 100}%, #e5e7eb 100%)`
                  }}
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>100 L/min</span>
                  <span>10,000 L/min</span>
                </div>
              </div>

              {/* Treatment Level */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                  Treatment Level
                </label>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                  {treatmentLevels.map(level => (
                    <motion.button
                      key={level.id}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => updateParam('treatmentLevel', level.id)}
                      className={`p-4 rounded-xl border-2 transition-all text-left ${
                        params.treatmentLevel === level.id
                          ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                          : 'border-gray-200 dark:border-gray-600 hover:border-primary-300'
                      }`}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-medium text-sm">{level.name}</span>
                        <span className="text-xs text-primary-600 dark:text-primary-400">
                          {level.multiplier}x
                        </span>
                      </div>
                      <p className="text-xs text-gray-600 dark:text-gray-400">{level.description}</p>
                    </motion.button>
                  ))}
                </div>
              </div>

              {/* Location and Urgency */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                    Location Type
                  </label>
                  <select
                    value={params.location}
                    onChange={(e) => updateParam('location', e.target.value)}
                    className="w-full px-4 py-3 rounded-xl border-2 border-gray-200 dark:border-gray-600 focus:border-primary-500 focus:ring-2 focus:ring-primary-500/20 transition-all bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  >
                    <option value="urban">Urban (1.0x)</option>
                    <option value="suburban">Suburban (1.1x)</option>
                    <option value="rural">Rural (1.3x)</option>
                    <option value="remote">Remote (1.6x)</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                    Project Urgency
                  </label>
                  <select
                    value={params.urgency}
                    onChange={(e) => updateParam('urgency', e.target.value)}
                    className="w-full px-4 py-3 rounded-xl border-2 border-gray-200 dark:border-gray-600 focus:border-primary-500 focus:ring-2 focus:ring-primary-500/20 transition-all bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  >
                    <option value="standard">Standard (1.0x)</option>
                    <option value="urgent">Urgent (1.3x)</option>
                    <option value="emergency">Emergency (1.8x)</option>
                  </select>
                </div>
              </div>

              {/* Additional Features */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                  Additional Features
                </label>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                  {additionalFeatures.map(feature => (
                    <motion.button
                      key={feature.id}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => toggleFeature(feature.id)}
                      className={`p-3 rounded-lg border-2 transition-all text-left ${
                        params.additionalFeatures.includes(feature.id)
                          ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                          : 'border-gray-200 dark:border-gray-600 hover:border-primary-300'
                      }`}
                    >
                      <div className="flex items-center justify-between mb-1">
                        <span className="font-medium text-sm">{feature.name}</span>
                        <span className="text-xs text-green-600 dark:text-green-400">
                          +{formatZAR(feature.cost)}
                        </span>
                      </div>
                      <p className="text-xs text-gray-600 dark:text-gray-400">{feature.description}</p>
                    </motion.button>
                  ))}
                </div>
              </div>

              {/* Maintenance Level */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                  Maintenance Level
                </label>
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
                  {[
                    { id: 'basic', name: 'Basic', description: 'Essential maintenance only', multiplier: 0.1 },
                    { id: 'standard', name: 'Standard', description: 'Regular maintenance schedule', multiplier: 0.15 },
                    { id: 'premium', name: 'Premium', description: 'Comprehensive maintenance', multiplier: 0.25 }
                  ].map(level => (
                    <motion.button
                      key={level.id}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => updateParam('maintenanceLevel', level.id)}
                      className={`p-3 rounded-lg border-2 transition-all text-left ${
                        params.maintenanceLevel === level.id
                          ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                          : 'border-gray-200 dark:border-gray-600 hover:border-primary-300'
                      }`}
                    >
                      <div className="flex items-center justify-between mb-1">
                        <span className="font-medium text-sm">{level.name}</span>
                        <span className="text-xs text-primary-600 dark:text-primary-400">
                          {(level.multiplier * 100).toFixed(0)}%
                        </span>
                      </div>
                      <p className="text-xs text-gray-600 dark:text-gray-400">{level.description}</p>
                    </motion.button>
                  ))}
                </div>
              </div>
            </motion.div>
          )}

          {activeTab === 'breakdown' && (
            <motion.div
              key="breakdown"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-6"
            >
              {/* Cost Breakdown Chart */}
              <div className="bg-gray-50 dark:bg-gray-900/50 rounded-xl p-6">
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Detailed Cost Breakdown
                </h4>

                <div className="space-y-4">
                  {[
                    { label: 'Equipment', amount: costBreakdown.equipment, color: 'bg-blue-500', percentage: (costBreakdown.equipment / costBreakdown.total * 100) },
                    { label: 'Installation', amount: costBreakdown.installation, color: 'bg-green-500', percentage: (costBreakdown.installation / costBreakdown.total * 100) },
                    { label: 'Annual Maintenance', amount: costBreakdown.maintenance, color: 'bg-yellow-500', percentage: (costBreakdown.maintenance / costBreakdown.total * 100) },
                    { label: 'Permits & Compliance', amount: costBreakdown.permits, color: 'bg-purple-500', percentage: (costBreakdown.permits / costBreakdown.total * 100) }
                  ].map((item, index) => (
                    <div key={index} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className={`w-4 h-4 rounded ${item.color}`}></div>
                          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                            {item.label}
                          </span>
                        </div>
                        <div className="text-right">
                          <span className="text-sm font-semibold text-gray-900 dark:text-white">
                            {formatZAR(item.amount)}
                          </span>
                          <span className="text-xs text-gray-500 ml-2">
                            ({item.percentage.toFixed(1)}%)
                          </span>
                        </div>
                      </div>
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <motion.div
                          className={`h-2 rounded-full ${item.color}`}
                          initial={{ width: 0 }}
                          animate={{ width: `${item.percentage}%` }}
                          transition={{ duration: 0.8, delay: index * 0.1 }}
                        />
                      </div>
                    </div>
                  ))}
                </div>

                <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                  <div className="flex items-center justify-between">
                    <span className="text-lg font-semibold text-gray-900 dark:text-white">Total Project Cost</span>
                    <span className="text-2xl font-bold text-primary-600 dark:text-primary-400">
                      {formatZAR(costBreakdown.total)}
                    </span>
                  </div>
                </div>
              </div>

              {/* Cost Factors */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                  <h5 className="font-semibold text-blue-900 dark:text-blue-300 mb-2">Cost Factors</h5>
                  <ul className="text-sm text-blue-800 dark:text-blue-400 space-y-1">
                    <li>• Service Type: {serviceOptions.find(s => s.id === params.serviceType)?.name}</li>
                    <li>• Treatment Level: {treatmentLevels.find(t => t.id === params.treatmentLevel)?.name}</li>
                    <li>• Location: {params.location.charAt(0).toUpperCase() + params.location.slice(1)}</li>
                    <li>• Urgency: {params.urgency.charAt(0).toUpperCase() + params.urgency.slice(1)}</li>
                    {params.additionalFeatures.length > 0 && (
                      <li>• Features: {params.additionalFeatures.length} selected</li>
                    )}
                  </ul>
                </div>

                <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
                  <h5 className="font-semibold text-green-900 dark:text-green-300 mb-2">Savings Opportunities</h5>
                  <ul className="text-sm text-green-800 dark:text-green-400 space-y-1">
                    <li>• Flexible timeline: Save up to 20%</li>
                    <li>• Standard location: Reduce complexity costs</li>
                    <li>• Bulk maintenance contracts: 15% discount</li>
                    <li>• Energy-efficient options available</li>
                  </ul>
                </div>
              </div>
            </motion.div>
          )}

          {activeTab === 'comparison' && (
            <motion.div
              key="comparison"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-6"
            >
              <div className="text-center">
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  Compare Treatment Options
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  See how different treatment levels affect your project cost
                </p>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                {treatmentLevels.map(level => {
                  const tempParams = { ...params, treatmentLevel: level.id };
                  const selectedService = serviceOptions.find(s => s.id === tempParams.serviceType);
                  if (!selectedService) return null;

                  let cost = selectedService.basePrice + (selectedService.pricePerUnit * tempParams.flowRate / 100);
                  cost *= level.multiplier;
                  cost *= locationMultipliers[tempParams.location as keyof typeof locationMultipliers] || 1.0;
                  cost *= urgencyMultipliers[tempParams.urgency as keyof typeof urgencyMultipliers] || 1.0;

                  const featuresCost = tempParams.additionalFeatures.reduce((total, featureId) => {
                    const feature = additionalFeatures.find(f => f.id === featureId);
                    return total + (feature?.cost || 0);
                  }, 0);
                  cost += featuresCost;

                  const totalCost = Math.round(cost * 1.4); // Including installation, maintenance, permits

                  return (
                    <motion.div
                      key={level.id}
                      whileHover={{ scale: 1.02 }}
                      className={`p-4 rounded-xl border-2 transition-all cursor-pointer ${
                        params.treatmentLevel === level.id
                          ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                          : 'border-gray-200 dark:border-gray-600 hover:border-primary-300'
                      }`}
                      onClick={() => updateParam('treatmentLevel', level.id)}
                    >
                      <div className="text-center">
                        <h5 className="font-semibold text-gray-900 dark:text-white mb-1">
                          {level.name}
                        </h5>
                        <p className="text-2xl font-bold text-primary-600 dark:text-primary-400 mb-2">
                          {formatZAR(totalCost)}
                        </p>
                        <p className="text-xs text-gray-600 dark:text-gray-400 mb-3">
                          {level.description}
                        </p>
                        <div className="text-xs text-gray-500">
                          Multiplier: {level.multiplier}x
                        </div>
                      </div>
                    </motion.div>
                  );
                })}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Cost Summary */}
      <div className="bg-gray-50 dark:bg-gray-900/50 p-4 sm:p-6 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-gray-600 dark:text-gray-400">Estimated Total Cost</p>
            <p className="text-2xl font-bold text-gray-900 dark:text-white">
              {formatZAR(costBreakdown.total)}
            </p>
          </div>
          <div className="text-right">
            <p className="text-xs text-gray-500 dark:text-gray-400">Equipment: {formatZAR(costBreakdown.equipment)}</p>
            <p className="text-xs text-gray-500 dark:text-gray-400">Installation: {formatZAR(costBreakdown.installation)}</p>
            <p className="text-xs text-gray-500 dark:text-gray-400">Annual Maintenance: {formatZAR(costBreakdown.maintenance)}</p>
          </div>
        </div>

        {/* Disclaimer */}
        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
          <div className="flex items-start space-x-2">
            <Info className="w-4 h-4 text-amber-500 mt-0.5 flex-shrink-0" />
            <div className="text-xs text-gray-600 dark:text-gray-400">
              <p className="font-medium text-amber-700 dark:text-amber-400 mb-1">Important Disclaimer</p>
              <p>
                These are preliminary cost estimates only and may vary based on site conditions,
                specifications, and market factors. The official detailed quote will be provided
                after a comprehensive site assessment and technical evaluation.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdvancedCalculator;
