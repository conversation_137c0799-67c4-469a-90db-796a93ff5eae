import React, { useEffect, useRef } from 'react';
import gsap from 'gsap';

const WaterWave: React.FC = () => {
  const pathRef1 = useRef<SVGPathElement>(null);
  const pathRef2 = useRef<SVGPathElement>(null);

  useEffect(() => {
    const ctx = gsap.context(() => {
      if (pathRef1.current && pathRef2.current) {
        // waveCycleWidth corresponds to the width of a single wave pattern (100 units in the SVG path).
        // The path 'd' attribute is defined with three such concatenated patterns (300 units total width)
        // to allow for seamless looping with buffer when translated by -waveCycleWidth.
        const waveCycleWidth = 100; 

        gsap.fromTo(pathRef1.current,
          { x: 0 }, 
          {
            x: -waveCycleWidth, 
            duration: 6,
            ease: 'linear',
            repeat: -1, 
          }
        );

        gsap.fromTo(pathRef2.current,
          { x: -waveCycleWidth / 3 }, 
          {
            x: -waveCycleWidth - (waveCycleWidth / 3), 
            duration: 8, 
            ease: 'linear',
            repeat: -1,
          }
        );
      }
    });

    return () => ctx.revert(); 
  }, []); 

  return (
    <div className="fixed bottom-0 left-0 w-full h-20 md:h-28 lg:h-32 z-10 pointer-events-none overflow-hidden">
      <svg
        className="w-full h-full"
        // viewBox defines the coordinate system: 0 0 is top-left, 100 width, 100 height.
        // This makes our 100-unit wave cycle fit the visible SVG area.
        viewBox="0 0 100 100" 
        preserveAspectRatio="none"
      >
        {/* Path is three wave cycles (300 units wide). Animating x by -100 makes the next cycle seamlessly replace the current one. */}
        <path
          ref={pathRef1}
          d="M0,50 Q25,70 50,50 T100,50 L100,100 L0,100 Z M100,50 Q125,70 150,50 T200,50 L200,100 L100,100 Z M200,50 Q225,70 250,50 T300,50 L300,100 L200,100 Z"
          fill="rgba(59, 130, 246, 0.3)" 
          transform="translate(0, 10)" // Slightly offset downwards for layering
        />
        {/* Second wave, also 300 units wide, for layered effect. */}
        <path
          ref={pathRef2}
          d="M0,60 Q25,40 50,60 T100,60 L100,100 L0,100 Z M100,60 Q125,40 150,60 T200,60 L200,100 L100,100 Z M200,60 Q225,40 250,60 T300,60 L300,100 L200,100 Z"
          fill="rgba(96, 165, 250, 0.2)"
        />
      </svg>
    </div>
  );
};

export default WaterWave;
