import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useIsMobile } from '../../hooks/useViewport';

interface WhatsAppButtonProps {
  phoneNumber?: string;
  message?: string;
  className?: string;
}

const WhatsAppButton: React.FC<WhatsAppButtonProps> = ({
  phoneNumber = '+27659643597',
  message = 'Hello! I would like to know more about your water treatment solutions.',
  className = '',
}) => {
  const isMobile = useIsMobile();
  const whatsappUrl = `https://wa.me/${phoneNumber.replace(/[^0-9]/g, '')}${
    message ? `?text=${encodeURIComponent(message)}` : ''
  }`;

  return (
    <AnimatePresence>
      <motion.div
        className={`fixed-bottom-right floating-button group ${className}`}
        initial={{ scale: 0, rotate: -180 }}
        animate={{ scale: 1, rotate: 0 }}
        exit={{ scale: 0, rotate: 180 }}
        transition={{
          delay: 1,
          duration: 0.5,
          type: "spring",
          stiffness: 200,
          damping: 15
        }}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        <motion.a
          href={whatsappUrl}
          target="_blank"
          rel="noopener noreferrer"
          className="relative overflow-hidden backdrop-blur-xl bg-green-500/90 hover:bg-green-600/90 border border-green-400/30 text-white p-3 sm:p-4 rounded-full shadow-2xl hover:shadow-3xl transition-all duration-300 ease-in-out flex items-center justify-center active:scale-90 focus:outline-none focus:ring-4 focus:ring-green-500/50"
          aria-label="Chat on WhatsApp"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
        >
          {/* Animated pulse ring */}
          <motion.div
            className="absolute inset-0 rounded-full bg-green-400/30 pointer-events-none"
            animate={{
              scale: [1, 1.3, 1],
              opacity: [0.7, 0, 0.7],
            }}
            transition={{
              duration: 2.5,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          />
          
          {/* Background gradient animation */}
          <div 
            className="absolute inset-0 -z-10 opacity-70 animate-pulse rounded-full"
            style={{
              background: 'linear-gradient(60deg, rgba(34, 197, 94, 0.3), rgba(22, 163, 74, 0.4) 50%, rgba(21, 128, 61, 0.3) 100%)',
              backgroundSize: '300% 300%',
            }}
          />
          
          {/* WhatsApp Icon */}
          <svg
            className="relative z-10 w-5 h-5 sm:w-6 sm:h-6 md:w-7 md:h-7"
            fill="currentColor"
            viewBox="0 0 24 24"
            aria-hidden="true"
          >
            <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.685"/>
          </svg>
          
          {/* Tooltip - Only show on desktop */}
          {!isMobile && (
            <motion.div
              className="absolute right-full mr-3 px-3 py-2 bg-gray-900/95 text-white text-xs sm:text-sm rounded-lg whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none backdrop-blur-sm"
              initial={{ x: 10, opacity: 0 }}
              whileHover={{ x: 0, opacity: 1 }}
            >
              Chat with us on WhatsApp
              <div className="absolute top-1/2 -right-1 w-2 h-2 bg-gray-900/95 rotate-45 transform -translate-y-1/2"></div>
            </motion.div>
          )}
        </motion.a>
      </motion.div>
    </AnimatePresence>
  );
};

export default WhatsAppButton;
