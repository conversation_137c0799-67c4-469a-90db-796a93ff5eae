import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Facebook, Twitter, Linkedin, Mail, MapPin, Phone, Droplet, Instagram, X, GanttChartSquare } from 'lucide-react';

const Footer: React.FC = () => {
  const [showPrivacy, setShowPrivacy] = useState(false);
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-gray-100 dark:bg-gray-800 pt-16 pb-8 relative overflow-hidden">
      <div className="absolute inset-0 opacity-5 dark:opacity-10 z-0">
        <div className="absolute inset-0 bg-water-pattern"></div>
      </div>

      <div className="container mx-auto px-4 md:px-6 relative z-10">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="space-y-4"
          >
            <div className="flex items-center space-x-2 text-primary-600 dark:text-primary-400">
              {/* <img src="/pazogen_logo.png" alt="Pazogen Logo" className="w-8 h-8 rounded-full shadow" /> */}
              <img
              src="/logoPazogen.png"
              alt="Pazogen Water & Wastewater Engineering Logo"
              className="h-23 w-auto object-contain"
            />
            </div>
            <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
              Leading provider of complete turnkey solutions for wastewater and water treatment.
              Committed to environmental sustainability and innovation.
            </p>
            <div className="flex space-x-4">
              <a href="https://www.facebook.com/share/16QSfYDVAB/?mibextid=wwXIfr" aria-label="Facebook" className="text-gray-500 hover:text-primary-600 dark:text-gray-400 dark:hover:text-primary-400 transition-colors">
                <Facebook size={20} />
              </a>
              <a href="https://www.linkedin.com/company/pazogen-water-wastewater-engineering/" aria-label="LinkedIn" className="text-gray-500 hover:text-primary-600 dark:text-gray-400 dark:hover:text-primary-400 transition-colors">
                <Linkedin size={20} />
              </a>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            viewport={{ once: true }}
            className="space-y-4"
          >
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Quick Links</h3>
            <ul className="space-y-2">
              {['#home', '#about', '#services', '/projects', '/gallery', '#contact'].map((item) => (
                <li key={item}>
                  <a 
                    href={`/${item.toLowerCase()}`} 
                    className="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
                  >
                    {item.slice(1)}
                  </a>
                </li>
              ))}
            </ul>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            viewport={{ once: true }}
            className="space-y-4"
          >
            
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            viewport={{ once: true }}
            className="space-y-4"
          >
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Contact</h3>
            <ul className="space-y-3">
              <li className="flex items-start space-x-3 text-gray-600 dark:text-gray-400">
                <MapPin size={18} className="flex-shrink-0 mt-1" />
                <span>8 Swartberg Road,
                    Alrode South,
                    Alberton
                    Johannesburg
                </span>
              </li>
              <li className="flex items-center space-x-3 text-gray-600 dark:text-gray-400">
                <Phone size={18} className="flex-shrink-0" />
                <span>+27(0) 10 109 6528</span>
              </li>
              <li className="flex items-center space-x-3 text-gray-600 dark:text-gray-400">
                <Mail size={18} className="flex-shrink-0" />
                <a href="mailto:<EMAIL>" className="hover:text-primary-600 dark:hover:text-primary-400 transition-colors"><EMAIL></a>
              </li>
            </ul>
          </motion.div>
        </div>

        <div className="pt-8 border-t border-gray-200 dark:border-gray-700">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-600 dark:text-gray-400 text-sm">
              &copy; {currentYear} Pazogen. All rights reserved.
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <button
                type="button"
                onClick={() => setShowPrivacy(true)}
                className="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 text-sm transition-colors focus:outline-none"
              >
                Privacy Policy
              </button>
            </div>
          </div>
        </div>

        {/* Privacy Policy Modal */}
        {showPrivacy && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
            <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg max-w-lg w-full p-6 relative">
              <button
                onClick={() => setShowPrivacy(false)}
                className="absolute top-2 right-2 text-gray-500 hover:text-primary-600 dark:hover:text-primary-400"
                aria-label="Close Privacy Policy"
              >
                <span aria-hidden="true">&times;</span>
              </button>
              <h2 className="text-xl font-bold mb-4 text-gray-900 dark:text-white">Privacy Policy</h2>
              <div className="text-gray-700 dark:text-gray-300 text-sm space-y-2 max-h-80 overflow-y-auto">
                <p>
                  Pazogen is committed to protecting your privacy. We do not collect personal information unless you voluntarily provide it to us. Any information you share will be used solely for the purpose of responding to your inquiries or providing our services.
                </p>
                <p>
                  We do not sell, trade, or otherwise transfer your information to outside parties. For more details, please contact us at <a href="mailto:<EMAIL>" className="text-primary-600 dark:text-primary-400 underline"><EMAIL></a>.
                </p>
                <p>
                  By using our website, you consent to our privacy policy.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </footer>
  );
};

export default Footer;