import React, { useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { ChevronDown, Droplet, Phone, ArrowRight } from 'lucide-react';
import { Link } from 'react-router-dom';
import gsap from 'gsap';

// Glass card component
const GlassCard: React.FC<{ className?: string; children: React.ReactNode }> = ({ children, className = '' }) => {
  const gradientRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (gradientRef.current) {
      // GSAP animation for a subtle, shifting gradient
      gsap.to(gradientRef.current, {
        '--gradient-color-1': 'rgba(59, 130, 246, 0.2)', // blue-500 with opacity
        '--gradient-color-2': 'rgba(96, 165, 250, 0.3)', // blue-400 with opacity
        '--gradient-color-3': 'rgba(34, 211, 238, 0.2)', // cyan-400 with opacity
        duration: 7,
        repeat: -1,
        yoyo: true,
        ease: 'sine.inOut',
      });
      gsap.to(gradientRef.current, {
        backgroundPosition: '200% center',
        duration: 15,
        repeat: -1,
        ease: 'linear',
      })
    }
  }, []);

  return (
    <div
      className={`relative backdrop-blur-xl bg-white/5 dark:bg-gray-900/5 border border-white/10 dark:border-gray-700/30 rounded-2xl shadow-2xl overflow-hidden transition-all duration-300 hover:shadow-3xl ${className}`}
      id="home"
    >
      <div
        ref={gradientRef}
        className="absolute inset-0 -z-10 opacity-70"
        style={{
          backgroundImage: 'linear-gradient(60deg, var(--gradient-color-1, rgba(59, 130, 246, 0.1)), var(--gradient-color-2, rgba(96, 165, 250, 0.15)) 50%, var(--gradient-color-3, rgba(34, 211, 238, 0.1)) 100%)',
          backgroundSize: '300% 300%', // Made larger for smoother animation
        }}
      />
      {children}
    </div>
  );
};

const Hero: React.FC = () => {
  const heroRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Floating animation for elements (kept from original)
    const floatingElements = document.querySelectorAll('.floating-element');
    floatingElements.forEach((el) => {
      gsap.to(el, {
        y: 8, // Slightly reduced y for subtlety
        duration: 3.5,
        repeat: -1,
        yoyo: true,
        ease: 'sine.inOut',
      });
    });
  }, []);
  
  // Updated to use landingpage.jpg from the public folder
  const heroBackgroundImage = 'url("/landingpage.jpg")'; 

  return (
    <section
      ref={heroRef}
      className="min-h-screen relative overflow-hidden flex items-center justify-center md:justify-start bg-cover bg-center p-3 sm:p-4 md:p-8 w-full"
      style={{
        backgroundImage: heroBackgroundImage,
        backgroundAttachment: window.innerWidth > 768 ? 'fixed' : 'scroll', // Disable parallax on mobile for performance
      }}
    >
      {/* Enhanced overlay for better mobile readability */}
      <div className="absolute inset-0 bg-black/50 sm:bg-black/40 dark:bg-black/70 dark:sm:bg-black/60 -z-0"></div>

      {/* Mobile-optimized content container */}
      <div className="container mx-auto px-3 sm:px-4 md:px-6 lg:px-8 relative z-10 w-full md:w-auto md:ml-[5%] lg:ml-[10%]">
        <GlassCard className="p-6 sm:p-8 md:p-10 lg:p-12 w-full md:max-w-xl lg:max-w-2xl">
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: 'easeOut' }}
            className="text-left" 
          >
           
            
            {/* Mobile-optimized heading */}
            <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-extrabold text-white dark:text-gray-50 mb-4 sm:mb-5 leading-tight tracking-tight antialiased">
              An Original Equipment Manufacturer (OEM) for Water and Wastewater Engineering
            </h1>

            {/* Mobile-optimized description */}
            <p className="text-base sm:text-lg md:text-xl text-gray-200 dark:text-gray-300 mb-8 sm:mb-10 max-w-3xl leading-relaxed antialiased">
              PAZOGEN is your trusted partner in water and wastewater solutions.
            </p>

            {/* Enhanced Mobile-First CTA Buttons */}
            <motion.div
              className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-start"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, duration: 0.6 }}
            >
              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Link
                  to="/get-a-quote"
                  className="group w-full sm:w-auto px-8 py-4 sm:py-3.5 bg-gradient-to-r from-blue-500 to-cyan-400 text-white font-bold sm:font-semibold rounded-2xl sm:rounded-lg hover:from-blue-600 hover:to-cyan-500 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 text-center inline-flex items-center justify-center text-base sm:text-sm active:scale-95"
                >
                  GET A FREE QUOTE
                  <ArrowRight className="w-5 h-5 ml-2 transition-transform duration-300 ease-in-out group-hover:translate-x-1" />
                </Link>
              </motion.div>

              {/* Calculator button temporarily disabled for next production release */}
              {/* <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Link
                  to="/calculator"
                  className="group w-full sm:w-auto px-8 py-4 sm:py-3.5 bg-white/10 backdrop-blur-sm border-2 border-white/20 text-white font-bold sm:font-semibold rounded-2xl sm:rounded-lg hover:bg-white/20 hover:border-white/30 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 text-center inline-flex items-center justify-center text-base sm:text-sm active:scale-95"
                >
                  COST CALCULATOR
                  <svg className="w-5 h-5 ml-2 transition-transform duration-300 ease-in-out group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                  </svg>
                </Link>
              </motion.div> */}
            </motion.div>
          </motion.div>
        </GlassCard>
      </div>
      


      {/* Enhanced Mobile Scroll Indicator */}
      <motion.div
        className="absolute bottom-20 sm:bottom-24 md:bottom-12 left-0 right-0 mx-auto w-fit flex flex-col items-center z-10"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1.2, duration: 0.8, ease: 'easeOut' }}
      >
        <motion.span
          className="text-xs sm:text-sm text-gray-200 dark:text-gray-300 mb-2 text-center font-medium"
          animate={{ opacity: [0.7, 1, 0.7] }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          Scroll to explore
        </motion.span>
        <motion.div
          animate={{ y: [0, 8, 0] }}
          transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
        >
          <ChevronDown className="w-5 h-5 sm:w-6 sm:h-6 text-gray-200 dark:text-gray-300" />
        </motion.div>
      </motion.div>
    </section>
  );
};

export default Hero;