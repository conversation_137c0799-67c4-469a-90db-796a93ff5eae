import React, { useRef } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { 
  Factory, 
  Lightbulb, 
  ClipboardList, 
  Filter, 
  Recycle, 
  Wrench,
  ArrowRight
} from 'lucide-react';

interface ServiceCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  index: number;
  image?: string;
}

const ServiceCard: React.FC<ServiceCardProps> = ({ icon, title, description, index, image }) => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, y: 30 }}
      animate={inView ? { opacity: 1, y: 0 } : {}}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      whileHover={{ y: -4 }}
      whileTap={{ scale: 0.98 }}
      className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-2xl p-4 sm:p-6 shadow-lg hover:shadow-xl relative overflow-hidden group border border-gray-200/50 dark:border-gray-700/50"
    >
      <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-primary-400 to-secondary-400 transform origin-left scale-x-0 group-hover:scale-x-100 transition-transform duration-300"></div>

      {/* Mobile-optimized image */}
      {image && (
        <div className="mb-4 w-full aspect-video rounded-xl overflow-hidden bg-gray-100 dark:bg-gray-900 flex items-center justify-center">
          <img
            src={image}
            alt={title}
            className="object-cover w-full h-full transition-transform duration-300 group-hover:scale-105"
            loading="lazy"
          />
        </div>
      )}

      {/* Mobile-optimized icon */}
      {icon && (
        <div className="mb-4 text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/30 p-2 sm:p-3 rounded-xl inline-block">
          {icon}
        </div>
      )}

      {/* Mobile-optimized title */}
      <h3 className="text-lg sm:text-xl font-display font-semibold text-gray-900 dark:text-white mb-3 leading-tight">
        {title}
      </h3>

      {/* Mobile-optimized description */}
      <div className="text-sm sm:text-base text-gray-600 dark:text-gray-300 mb-4 leading-relaxed">
        {description.split('\n').map((line, i) => (
          <div key={i} className="mb-1">{line}</div>
        ))}
      </div>

      {/* Mobile-optimized CTA */}
      <motion.a
        href="#contact"
        className="inline-flex items-center text-primary-600 dark:text-primary-400 font-semibold group-hover:text-primary-700 dark:group-hover:text-primary-300 transition-colors text-sm sm:text-base active:scale-95"
        whileTap={{ scale: 0.95 }}
      >
        Learn more
        <motion.span
          initial={{ x: 0 }}
          whileHover={{ x: 4 }}
          className="ml-1"
        >
          <ArrowRight size={14} className="sm:w-4 sm:h-4" />
        </motion.span>
      </motion.a>
    </motion.div>
  );
};

const Services: React.FC = () => {
  const sectionRef = useRef<HTMLElement>(null);
  const bgRef = useRef<HTMLDivElement>(null);
  
  // Enhanced scroll-based parallax effect
  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: ["start end", "end start"]
  });
  
  // Vertical parallax movement
  const y = useTransform(scrollYProgress, [0, 1], ["-30%", "30%"]);
  const services = [
    {
      title: 'Screen Equipment',
      image: '/ScreenEquipment.jpeg',
      description: [
        '• Mechanical Front Rake screens',
        '• Fine and ultra-fine screens',
        '• Manual hand rake screens',
        '• Perforated screens',
      ].join('\n'),
    },
    {
      title: 'Flow Control',
      image: '/FlowControl.jpeg',
      description: [
        '• Sluice gate and Penstocks',
        '• Hand stops',
        '• Tilting Weirs',
        '• Stop Logs',
        '• Downwards opening gates',
      ].join('\n'),
    },
    {
      title: 'Clarifiers',
      image: '/Clarifiers.jpeg',
      description: [
        '• Peripheral drive settling tanks',
        '• Peripheral drive suction lift',
      ].join('\n'),
    }
  ];

  return (
    <section
      id="services"
      ref={sectionRef}
      className="py-12 sm:py-16 md:py-20 bg-gray-50 dark:bg-gray-900/95 relative overflow-hidden"
    >
      {/* Mobile-optimized Background Image with Parallax Effect */}
      <motion.div
        ref={bgRef}
        className="absolute inset-0 w-full h-full bg-cover bg-center opacity-95"
        style={{
          backgroundImage: "url('/backgroundServices.png')",
          backgroundAttachment: window.innerWidth > 768 ? 'fixed' : 'scroll', // Disable parallax on mobile
          y: window.innerWidth > 768 ? y : 0,
          willChange: 'transform',
          transition: 'transform 0.1s ease-out'
        }}
      />

      {/* Enhanced Mobile Gradient Overlay */}
      <div className="absolute inset-0 bg-gradient-to-b from-white/85 via-white/70 to-white/85 dark:from-gray-900/95 dark:via-gray-900/90 dark:to-gray-900/95 sm:from-white/80 sm:via-transparent sm:to-white/80 dark:sm:from-gray-900/90 dark:sm:via-gray-900/95 dark:sm:to-gray-900/90" />

      <div className="container mx-auto px-3 sm:px-4 md:px-6 relative z-10">
        {/* Mobile-optimized header */}
        <div className="text-center max-w-3xl mx-auto mb-8 sm:mb-12 md:mb-16">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
            className="text-2xl sm:text-3xl md:text-4xl font-display font-bold text-gray-900 dark:text-white mb-3 sm:mb-4 leading-tight"
          >
            Our <span className="text-primary-600 dark:text-primary-400">Services</span>
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="text-gray-600 dark:text-gray-300 text-base sm:text-lg leading-relaxed px-2 sm:px-0"
          >
            We provide comprehensive water treatment solutions tailored to meet the unique
            needs of municipalities, industries, and commercial developments.
          </motion.p>
        </div>

        {/* Mobile-optimized grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 md:gap-8">
          {services.map((service, index) => (
            <ServiceCard
              key={index}
              icon={null}
              title={service.title}
              description={service.description}
              image={service.image}
              index={index}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default Services;