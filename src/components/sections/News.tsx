import React, { useState, useCallback, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Calendar, ArrowRight, X, ChevronLeft, ChevronRight } from 'lucide-react';

interface NewsImage {
  url: string;
  alt: string;
}

interface NewsItem {
  id: string;
  title: string;
  date: string;
  content: string;
  images: NewsImage[];
  link?: string;
}

// Example news item - replace with real data
const newsItems: NewsItem[] = [
  {
    id: '1',
    title: '<PERSON>ogen at ENLIT AFRICA EXHIBITION 2025',
    date: 'May 20, 2025',
    content: 'We are thrilled to announce <PERSON><PERSON>\'s first exhibition at #EnlitAfrica 2025, held at the CTICC from May 20–22. It was an incredible success 🎊! Our booth showcased cutting-edge solutions in water and wastewater treatment, and we were amazed by the enthusiasm and engagement from attendees 🤝.\n\nA huge thank you to everyone who visited, connected with us, and made this event an unforgettable experience!',
    images: [
      { url: '/News1.jpg', alt: 'Pazogen Exhibition Booth' },
      { url: '/News2.jpg', alt: 'Team Presentation at ENLIT Africa' },
      { url: '/News3.jpg', alt: 'Product Demonstration' },
      { url: '/News4.jpg', alt: 'Networking Event' },
      { url: '/News5.jpg', alt: 'Award Ceremony' }
    ],
    link: '/news/enlit-africa-2025'
  }
];

const News: React.FC = () => {
  const [selectedImage, setSelectedImage] = useState<{ newsId: string; index: number } | null>(null);
  const [currentSlide, setCurrentSlide] = useState<{ [key: string]: number }>({});
  const [touchStart, setTouchStart] = useState<number | null>(null);

  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStart(e.touches[0].clientX);
  };

  const handleTouchEnd = (e: React.TouchEvent, newsId: string, imagesLength: number) => {
    if (touchStart === null) return;
    
    const touchEnd = e.changedTouches[0].clientX;
    const diff = touchStart - touchEnd;
    
    if (Math.abs(diff) > 50) { // Minimum swipe distance
      if (diff > 0) {
        // Swipe left
        handleNext(newsId, imagesLength);
      } else {
        // Swipe right
        handlePrev(newsId, imagesLength);
      }
    }
    setTouchStart(null);
  };

  const handleNext = useCallback((newsId: string, imagesLength: number) => {
    setCurrentSlide(prev => ({
      ...prev,
      [newsId]: ((prev[newsId] || 0) + 1) % imagesLength
    }));
  }, []);

  const handlePrev = useCallback((newsId: string, imagesLength: number) => {
    setCurrentSlide(prev => ({
      ...prev,
      [newsId]: ((prev[newsId] || 0) - 1 + imagesLength) % imagesLength
    }));
  }, []);

  const handleImageClick = (newsId: string, index: number) => {
    setSelectedImage({ newsId, index });
    // Update current slide to match clicked image
    setCurrentSlide(prev => ({ ...prev, [newsId]: index }));
  };

  const handleModalClose = () => {
    setSelectedImage(null);
  };

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!selectedImage) return;
      
      const currentNews = newsItems.find(item => item.id === selectedImage.newsId);
      if (!currentNews) return;

      switch (e.key) {
        case 'ArrowRight':
          handleNext(selectedImage.newsId, currentNews.images.length);
          setSelectedImage(prev => prev ? { ...prev, index: (prev.index + 1) % currentNews.images.length } : null);
          break;
        case 'ArrowLeft':
          handlePrev(selectedImage.newsId, currentNews.images.length);
          setSelectedImage(prev => prev ? { ...prev, index: (prev.index - 1 + currentNews.images.length) % currentNews.images.length } : null);
          break;
        case 'Escape':
          handleModalClose();
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [selectedImage, handleNext, handlePrev]);

  return (
    <section id="news" className="py-20 bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 md:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="mb-12 text-center"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
            Latest News
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            Stay updated with our latest achievements, events, and innovations in water treatment solutions.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 gap-8">
          {newsItems.map((item) => (
            <motion.article
              key={item.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="bg-white/70 dark:bg-gray-800/50 backdrop-blur-xl rounded-2xl shadow-2xl p-6 md:p-8 border border-white/20 dark:border-gray-700/30"
            >
              <div className="flex flex-col lg:flex-row gap-8">
                {/* Image Gallery */}
                <div className="lg:w-1/2">
                  <div 
                    className="relative overflow-hidden rounded-xl aspect-video bg-gray-100 dark:bg-gray-700"
                    onTouchStart={handleTouchStart}
                    onTouchEnd={(e) => handleTouchEnd(e, item.id, item.images.length)}
                  >
                    <motion.div
                      initial={false}
                      animate={{ x: `${-(currentSlide[item.id] || 0) * 100}%` }}
                      transition={{ type: "spring", stiffness: 300, damping: 30 }}
                      className="flex w-full h-full"
                    >
                      {item.images.map((image, index) => (
                        <motion.div
                          key={index}
                          className="relative w-full h-full flex-shrink-0"
                          whileHover={{ scale: 1.02 }}
                          onClick={() => handleImageClick(item.id, index)}
                        >
                          <img
                            src={image.url}
                            alt={image.alt}
                            className="w-full h-full object-cover cursor-pointer"
                          />
                          <div className="absolute inset-0 bg-black/0 hover:bg-black/10 transition-colors duration-200" />
                        </motion.div>
                      ))}
                    </motion.div>

                    {/* Navigation arrows */}
                    <div className="absolute inset-0 flex items-center justify-between p-4 opacity-0 hover:opacity-100 transition-opacity">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handlePrev(item.id, item.images.length);
                        }}
                        className="bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-colors transform hover:scale-110"
                        aria-label="Previous image"
                      >
                        <ChevronLeft size={24} />
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleNext(item.id, item.images.length);
                        }}
                        className="bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-colors transform hover:scale-110"
                        aria-label="Next image"
                      >
                        <ChevronRight size={24} />
                      </button>
                    </div>

                    {/* Dots indicator */}
                    <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex gap-2 z-10">
                      {item.images.map((_, index) => (
                        <button
                          key={index}
                          onClick={(e) => {
                            e.stopPropagation();
                            setCurrentSlide(prev => ({ ...prev, [item.id]: index }));
                          }}
                          className={`w-2 h-2 rounded-full transition-all ${
                            index === (currentSlide[item.id] || 0)
                              ? 'bg-white scale-125'
                              : 'bg-white/50 hover:bg-white/75'
                          }`}
                          aria-label={`Go to image ${index + 1}`}
                        />
                      ))}
                    </div>
                  </div>
                </div>

                {/* Content */}
                <div className="lg:w-1/2">
                  <div className="flex items-center gap-2 text-primary-600 dark:text-primary-400 mb-3">
                    <Calendar size={18} />
                    <span className="text-sm font-medium">{item.date}</span>
                  </div>
                  <h3 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-4">
                    {item.title}
                  </h3>
                  <div className="prose prose-lg dark:prose-invert">
                    {item.content.split('\n\n').map((paragraph, index) => (
                      <p key={index} className="text-gray-600 dark:text-gray-300 mb-4">
                        {paragraph}
                      </p>
                    ))}
                  </div>
                  
                </div>
              </div>
            </motion.article>
          ))}
        </div>
      </div>

      {/* Fullscreen Modal */}
      <AnimatePresence>
        {selectedImage && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={handleModalClose}
            className="fixed inset-0 bg-black/95 z-50 backdrop-blur-sm"
          >
            <div className="absolute top-4 right-4 z-50 flex gap-4">
              <button
                onClick={handleModalClose}
                className="bg-white/10 hover:bg-white/20 text-white p-2 rounded-full transition-colors"
                aria-label="Close modal"
              >
                <X size={24} />
              </button>
            </div>

            <div className="absolute inset-0 flex items-center justify-center p-4">
              {newsItems.map((item) => {
                if (item.id !== selectedImage.newsId) return null;
                const image = item.images[currentSlide[item.id] || 0];
                
                return (
                  <motion.div
                    key={`modal-${item.id}`}
                    className="relative w-full max-w-7xl max-h-[90vh]"
                    initial={{ scale: 0.9, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    exit={{ scale: 0.9, opacity: 0 }}
                    onClick={(e) => e.stopPropagation()}
                  >
                    <img
                      src={image.url}
                      alt={image.alt}
                      className="w-full h-auto max-h-[90vh] object-contain rounded-lg"
                    />
                    <div className="absolute bottom-0 left-0 right-0 bg-black/50 text-white p-4 rounded-b-lg">
                      <p className="text-center">{image.alt}</p>
                      <p className="text-center text-sm text-gray-300">
                        Image {(currentSlide[item.id] || 0) + 1} of {item.images.length}
                      </p>
                    </div>
                  </motion.div>
                );
              })}
            </div>

            {/* Modal Navigation */}
            <div className="absolute inset-y-0 left-0 right-0 flex items-center justify-between px-4 pointer-events-none">
              {newsItems.map((item) => {
                if (item.id !== selectedImage.newsId) return null;
                return (
                  <React.Fragment key={`nav-${item.id}`}>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handlePrev(item.id, item.images.length);
                      }}
                      className="pointer-events-auto bg-black/50 hover:bg-black/70 text-white p-3 rounded-full transition-all transform hover:scale-110"
                      aria-label="Previous image"
                    >
                      <ChevronLeft size={32} />
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleNext(item.id, item.images.length);
                      }}
                      className="pointer-events-auto bg-black/50 hover:bg-black/70 text-white p-3 rounded-full transition-all transform hover:scale-110"
                      aria-label="Next image"
                    >
                      <ChevronRight size={32} />
                    </button>
                  </React.Fragment>
                );
              })}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </section>
  );
};

export default News;