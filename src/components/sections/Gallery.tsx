import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, PlayCircle } from 'lucide-react';

// All gallery media (excluding logos/icons)
const galleryMedia = [
  // Landscape/rectangular images
  // { src: '/BioFilters.jpeg', alt: 'Biological Filters', type: 'image' },
  { src: '/Clarifiers.jpeg', alt: 'Clarifiers', type: 'image' },
  { src: '/Conveyors.jpeg', alt: 'Conveyors', type: 'image' },
  { src: '/drawings.png', alt: 'As-built Drawings', type: 'image' },
  { src: '/FlowControl.jpeg', alt: 'Flow Control', type: 'image' },
  { src: '/MixerSystem.jpeg', alt: 'Mixer System', type: 'image' },
  { src: '/ScreenEquipment.jpeg', alt: 'Screen Equipment', type: 'image' },
  // Project/people/other images
  // { src: '/heroback.png', alt: 'Hero Background', type: 'image' },
  { src: '/enerdalle2.png', alt: 'Ennerdale Project 2', type: 'image' },
  { src: '/enerdalle3.png', alt: 'Ennerdale Project 3', type: 'image' },
  { src: '/Edernville1.png', alt: 'Edernville Project 1', type: 'image' },
  { src: '/mogalecity1.png', alt: 'Mogale City Project 1', type: 'image' },
  { src: '/mogalecity2.png', alt: 'Mogale City Project 2', type: 'image' },
  { src: '/Pazo_gal1.png', alt: 'Pazo Gallery 1', type: 'image' },
  { src: '/Pazogen_gal1.png', alt: 'Pazogen Gallery 1', type: 'image' },
  { src: '/Pazogen_gal2.png', alt: 'Pazogen Gallery 2', type: 'image' },
  { src: '/Pazogen_gal3.png', alt: 'Pazogen Gallery 3', type: 'image' },
  { src: '/Pazogen_gla4.png', alt: 'Pazogen Gallery 4', type: 'image' },
  { src: '/vaalbank1.png', alt: 'Vaalbank Project', type: 'image' },

  //News
  { src: '/News1.jpg', alt: 'Pazogen Exhibition Booth', type: 'image' },
  { src: '/News2.jpg', alt: 'Team Presentation at ENLIT Africa', type: 'image' },
  { src: '/News3.jpg', alt: 'Product Demonstration', type: 'image' },
  { src: '/News4.jpg', alt: 'Networking Event', type: 'image' },
];

const galleryVideos = [
  { src: '/Video1.mp4', alt: 'Plant Walkthrough Video', poster: '/MixerSystem.jpeg' },
  { src: '/video2.mp4', alt: 'Equipment in Action', poster: '/Pazogen_gal1.png' },
];

const hexClip =
  'polygon(25% 6.7%, 75% 6.7%, 100% 50%, 75% 93.3%, 25% 93.3%, 0% 50%)';

const Gallery: React.FC = () => {
  const [lightbox, setLightbox] = useState<null | { src: string; type: 'image' | 'video'; alt: string }>(null);
  const [selectedIdx, setSelectedIdx] = useState(0);
  const selectedMedia = galleryMedia[selectedIdx];

  // Responsive honeycomb: 3 per row on mobile, 5+ on desktop, staggered rows
  return (
    <section id="gallery" className="py-20 bg-gradient-to-br from-primary-50/60 via-white/80 to-blue-50/60 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 min-h-screen relative overflow-x-hidden">
      <div className="absolute inset-0 bg-water-pattern opacity-10 pointer-events-none z-0" />
      <div className="container mx-auto px-4 md:px-8 relative z-10">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
            className="text-4xl md:text-5xl font-display font-extrabold text-primary-700 dark:text-primary-200 mb-4 drop-shadow-lg"
          >
            Gallery <span className="text-primary-500 dark:text-primary-400">Showcase</span>
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="text-gray-700 dark:text-gray-300 text-lg"
          >
            Explore our projects, equipment, and team in action. Click any image or video for a closer look!
          </motion.p>
        </div>
        {/* Featured Image + Horizontal Scrollable Thumbnails */}
        <div className="relative flex flex-col items-center w-full max-w-4xl mx-auto select-none">
          {/* Animated gradient background for extra modern look */}
          <motion.div
            aria-hidden
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 1 }}
            className="absolute inset-0 z-0 pointer-events-none"
            style={{
              background:
                'radial-gradient(ellipse 80% 60% at 50% 40%, rgba(34,211,238,0.18) 0%, rgba(59,130,246,0.12) 60%, transparent 100%)',
              filter: 'blur(12px)',
            }}
          />
          {/* Featured large image (dynamic) */}
          {selectedMedia && (
            <motion.div
              className="w-full max-w-3xl aspect-[4/3] rounded-2xl overflow-hidden shadow-xl mb-6 relative group cursor-pointer bg-white/10"
              initial={{ opacity: 0, y: 40 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, type: 'spring', stiffness: 60 }}
              onClick={() => setLightbox({ src: selectedMedia.src, type: 'image', alt: selectedMedia.alt })}
            >
              <img
                src={selectedMedia.src}
                alt={selectedMedia.alt}
                className="w-full h-full object-contain object-center transition-transform duration-700 group-hover:scale-105"
                loading="lazy"
                draggable={false}
                style={{ aspectRatio: 'auto' }}
              />
              {/* Overlay on hover */}
              <motion.div
                className="absolute inset-0 flex items-center justify-center z-10"
                initial={{ opacity: 0 }}
                whileHover={{ opacity: 1 }}
                transition={{ duration: 0.25 }}
              >
                <div className="backdrop-blur-md bg-gradient-to-br from-cyan-400/30 via-blue-500/20 to-transparent border border-cyan-300/40 rounded-xl shadow-xl px-4 py-2">
                  <span className="text-white text-lg font-semibold drop-shadow-lg text-center pointer-events-none" style={{ textShadow: '0 0 8px #38bdf8, 0 0 2px #fff' }}>
                    {selectedMedia.alt}
                  </span>
                </div>
              </motion.div>
            </motion.div>
          )}
          {/* Horizontally scrollable thumbnails */}
          <div className="w-full overflow-x-auto pb-2 mb-2">
            <div className="flex flex-row gap-3 min-w-fit px-1" style={{ WebkitOverflowScrolling: 'touch' }}>
              {galleryMedia.map((media, i) => (
                <motion.div
                  key={media.src}
                  className={`relative group cursor-pointer rounded-xl border-2 transition-all duration-200 ${i === selectedIdx ? 'border-cyan-400 shadow-lg scale-105' : 'border-transparent opacity-80 hover:opacity-100'}`}
                  style={{ minWidth: 110, minHeight: 80, maxWidth: 140, aspectRatio: '4/3', background: '#f3f6fa' }}
                  onClick={() => setSelectedIdx(i)}
                  whileHover={{ scale: 1.08 }}
                  animate={{ zIndex: i === selectedIdx ? 2 : 1 }}
                >
                  <img
                    src={media.src}
                    alt={media.alt}
                    className="w-full h-full object-cover object-center rounded-xl"
                    loading="lazy"
                    draggable={false}
                  />
                  {i === selectedIdx && (
                    <div className="absolute inset-0 border-2 border-cyan-400 rounded-xl pointer-events-none" style={{ boxShadow: '0 0 12px 2px #38bdf8' }} />
                  )}
                </motion.div>
              ))}
            </div>
          </div>
        </div>
        {/* Video Section */}
        <div className="mt-20">
          <motion.h3
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
            className="text-3xl md:text-4xl font-display font-bold text-primary-700 dark:text-primary-200 mb-8 text-center"
          >
            Videos <span className="text-primary-500 dark:text-primary-400">in Action</span>
          </motion.h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {galleryVideos.map((video, i) => (
              <motion.div
                key={video.src}
                className="relative group rounded-2xl overflow-hidden shadow-xl border border-primary-100 dark:border-primary-800 bg-white/80 dark:bg-gray-900/60 cursor-pointer"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: i * 0.1 }}
                whileHover={{ scale: 1.03, zIndex: 2 }}
                onClick={() => setLightbox({ src: video.src, type: 'video', alt: video.alt })}
              >
                <img
                  src={video.poster}
                  alt={video.alt}
                  className="w-full h-64 object-cover object-center group-hover:scale-105 transition-transform duration-500"
                  loading="lazy"
                  draggable={false}
                />
                <div className="absolute inset-0 flex items-center justify-center bg-black/30 group-hover:bg-black/50 transition-colors duration-300">
                  <PlayCircle size={64} className="text-primary-200 drop-shadow-lg group-hover:scale-110 transition-transform duration-300" />
                </div>
                <div className="absolute bottom-4 left-4 bg-primary-700/80 text-white px-4 py-2 rounded-lg font-semibold shadow-lg text-lg">
                  {video.alt}
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
      {/* Lightbox for images and videos */}
      <AnimatePresence>
        {lightbox && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4"
            onClick={() => setLightbox(null)}
          >
            <motion.div
              initial={{ scale: 0.95, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.95, opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="relative max-w-5xl w-full h-auto flex flex-col items-center"
              onClick={e => e.stopPropagation()}
            >
              <button
                onClick={() => setLightbox(null)}
                className="absolute top-4 right-4 bg-white/20 rounded-full p-2 text-white hover:bg-white/40 transition-colors z-10"
                aria-label="Close"
              >
                <X size={28} />
              </button>
              {lightbox.type === 'image' ? (
                <img
                  src={lightbox.src}
                  alt={lightbox.alt}
                  className="w-full h-auto rounded-xl object-contain max-h-[80vh] shadow-2xl"
                />
              ) : (
                <video
                  src={lightbox.src}
                  controls
                  autoPlay
                  className="w-full h-auto rounded-xl object-contain max-h-[80vh] shadow-2xl bg-black"
                  poster={galleryVideos.find(v => v.src === lightbox.src)?.poster}
                />
              )}
              <div className="mt-4 text-white text-center">
                <p className="text-xl font-bold mb-1">{lightbox.alt}</p>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </section>
  );
};

export default Gallery;