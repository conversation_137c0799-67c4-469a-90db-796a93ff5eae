import React, { useRef } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import { useInView } from 'react-intersection-observer';

const Team: React.FC = () => {
  const sectionRef = useRef<HTMLElement>(null);
  const bgRef = useRef<HTMLDivElement>(null);
  
  // Scroll-based parallax effect
  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: ["start end", "end start"]
  });
  
  // Vertical parallax movement
  const y = useTransform(scrollYProgress, [0, 1], ["-30%", "30%"]);
  
  // Animation variants for cards
  const cardVariants = {
    hidden: { opacity: 0, y: 30, scale: 0.95 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        delay: i * 0.1,
        duration: 0.6,
        ease: [0.25, 0.1, 0.25, 1.0]
      }
    }),
    hover: {
      y: -5,
      boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
      transition: {
        duration: 0.3,
        ease: 'easeOut'
      }
    }
  };
  
  // Animation variant for the MD card
  const mdCardVariants = {
    hidden: { opacity: 0, y: 50, scale: 0.95 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.8,
        ease: [0.25, 0.1, 0.25, 1.0]
      }
    },
    hover: {
      y: -5,
      boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
      transition: {
        duration: 0.3,
        ease: 'easeOut'
      }
    }
  };
  
  // Generate a subtle gradient based on name for consistent colors
  const getGradient = (name: string) => {
    const colors = [
      'from-blue-500 to-cyan-400',
      'from-purple-500 to-pink-400',
      'from-green-500 to-emerald-400',
      'from-amber-500 to-yellow-400',
      'from-rose-500 to-pink-400',
      'from-indigo-500 to-blue-400'
    ];
    const index = name.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0) % colors.length;
    return colors[index];
  };
  
  // InView hook for the MD card
  const [mdCardRef, mdCardInView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });
  
  // InView hook for team cards
  const [teamCardsRef, teamCardsInView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  return (
    <section 
      ref={sectionRef}
      className="relative w-full py-20 overflow-hidden"
    >
      {/* Background Image with Parallax Effect */}
      <motion.div 
        ref={bgRef}
        className="absolute inset-0 w-full h-full bg-cover bg-center opacity-95"
        style={{
          backgroundImage: "url('/meettheteam.png')",
          backgroundAttachment: 'fixed',
          y: y,
          willChange: 'transform',
          transition: 'transform 0.1s ease-out'
        }}
      />
      
      {/* Gradient Overlay */}
      <div className="absolute inset-0 bg-gradient-to-b from-white/80 via-transparent to-white/80 dark:from-gray-900/90 dark:via-gray-900/95 dark:to-gray-900/90" />
      
      <div className="relative z-10">
        <div id="leadership-container" className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-2">
              Meet the team
            </h2>
            <div className="w-16 h-1 bg-gradient-to-r from-primary-500 to-primary-300 mx-auto"></div>
          </div>

        {/* Managing Director */}
        <motion.div 
          ref={mdCardRef}
          initial="hidden"
          animate={mdCardInView ? "visible" : "hidden"}
          variants={mdCardVariants}
          className="relative z-10 max-w-md mx-auto mb-12 group"
        >
          <motion.div 
            className="relative overflow-hidden bg-white/70 dark:bg-gray-800/70 backdrop-blur-lg rounded-2xl p-6 text-center border border-white/20 dark:border-gray-700/50 shadow-lg group"
            data-animate="md"
            whileHover="hover"
          >
            {/* Animated background on hover */}
            <motion.div 
              className="absolute inset-0 bg-gradient-to-br from-primary-500/5 to-primary-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
              initial={{ opacity: 0 }}
              whileHover={{ opacity: 1 }}
            />
            
            <div className="relative z-10">
              <div className="w-24 h-24 mx-auto mb-5 rounded-2xl bg-gradient-to-br from-primary-500 to-primary-600 flex items-center justify-center text-white text-3xl font-bold shadow-lg transition-all duration-300 group-hover:shadow-primary-500/30 group-hover:scale-105">
                MG
              </div>
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-1.5">Mlindi Gumede</h3>
              <p className="text-primary-600 dark:text-primary-400 font-medium bg-white/50 dark:bg-gray-800/50 px-4 py-1.5 rounded-full text-sm inline-block backdrop-blur-sm">
                Managing Director
              </p>
              
              {/* Decorative elements */}
              <div className="absolute -top-4 -right-4 w-16 h-16 rounded-full bg-primary-500/10 dark:bg-primary-400/10 -z-10" />
              <div className="absolute -bottom-6 -left-6 w-20 h-20 rounded-full bg-primary-500/5 dark:bg-primary-400/5 -z-10" />
            </div>
          </motion.div>
        </motion.div>

        {/* Leadership Team */}
        <motion.div 
          ref={teamCardsRef}
          className="grid grid-cols-2 md:grid-cols-3 gap-6 max-w-5xl mx-auto px-4"
        >
          {[
            { name: 'T. Malomane', role: 'Engineering' },
            { name: 'L. Matsose', role: 'Production' },
            { name: 'W. Mashele', role: 'Projects' },
            { name: 'T. Duma', role: 'Sales' },
            { name: 'N. Mkhwanazi', role: 'Finance' },
            { name: 'P. Chiripamberi', role: 'Tendering' }
          ].map((person, index) => (
            <motion.div 
              key={person.name}
              className="relative group"
              custom={index}
              initial="hidden"
              animate={teamCardsInView ? "visible" : "hidden"}
              whileHover="hover"
              variants={cardVariants}
            >
              <div className="relative h-full bg-white/70 dark:bg-gray-800/70 backdrop-blur-lg rounded-xl p-5 text-center border border-white/20 dark:border-gray-700/50 shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden">
                {/* Animated background on hover */}
                <motion.div 
                  className={`absolute inset-0 bg-gradient-to-br ${getGradient(person.name)}/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500`}
                  initial={{ opacity: 0 }}
                  whileHover={{ opacity: 1 }}
                />
                
                <div className="relative z-10 h-full flex flex-col">
                  <div className={`w-20 h-20 mx-auto mb-4 rounded-2xl ${getGradient(person.name)} flex items-center justify-center text-white text-2xl font-bold shadow-lg transition-all duration-300 group-hover:scale-110`}>
                    {person.name.split(' ').map(n => n[0]).join('')}
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-1.5">{person.name}</h3>
                  <p className="text-gray-600 dark:text-gray-300 font-medium text-sm bg-white/50 dark:bg-gray-800/50 px-3 py-1 rounded-full inline-block backdrop-blur-sm mt-auto">
                    {person.role}
                  </p>
                  
                  {/* Decorative elements */}
                  <div className="absolute -top-3 -right-3 w-12 h-12 rounded-full bg-primary-500/5 dark:bg-primary-400/5 -z-10" />
                  <div className="absolute -bottom-4 -left-4 w-16 h-16 rounded-full bg-primary-500/5 dark:bg-primary-400/5 -z-10" />
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </div>
    </section>
  );
};

export default Team;
