import React, { useState, useRef } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import {
  Calculator as CalculatorIcon,
  ArrowRight,
  Download,
  Mail,
  Phone,
  MessageCircle
} from 'lucide-react';
import AdvancedCalculator, { CalculatorParams, CostBreakdown } from '../../components/ui/AdvancedCalculator';
import { formatZAR } from '../../utils/currency';

const Calculator: React.FC = () => {
  const navigate = useNavigate();
  const calculatorRef = useRef<HTMLDivElement>(null);
  const [calculationData, setCalculationData] = useState<{
    params: CalculatorParams;
    cost: CostBreakdown;
  } | null>(null);

  // Handle calculation changes
  const handleCalculationChange = (params: CalculatorParams, cost: CostBreakdown) => {
    setCalculationData({ params, cost });
  };

  // Generate PDF quote (placeholder)
  const generatePDF = () => {
    if (!calculationData) return;
    
    // In a real implementation, this would generate a PDF
    console.log('Generating PDF with data:', calculationData);
    alert('PDF generation would be implemented here. For now, check the console for quote data.');
  };

  // Navigate to full quote request
  const proceedToQuote = () => {
    if (calculationData) {
      // Pass calculation data to quote request
      navigate('/get-a-quote', { 
        state: { 
          calculatorData: calculationData 
        } 
      });
    } else {
      navigate('/get-a-quote');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 py-8 sm:py-12">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8 sm:mb-12"
        >
          <div className="flex items-center justify-center mb-4">
            <div className="bg-primary-100 dark:bg-primary-900/30 p-3 rounded-full">
              <CalculatorIcon className="w-8 h-8 text-primary-600 dark:text-primary-400" />
            </div>
          </div>
          
          <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-4">
            Water Treatment <span className="text-primary-600 dark:text-primary-400">Calculator</span>
          </h1>
          
          <p className="text-lg sm:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Get instant cost estimates for your water treatment project. Configure your requirements 
            and see real-time pricing with detailed breakdowns.
          </p>
        </motion.div>

        {/* Main Calculator */}
        <motion.div
          ref={calculatorRef}
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="max-w-6xl mx-auto mb-8"
        >
          <AdvancedCalculator
            onCalculationChange={handleCalculationChange}
            className="w-full"
          />
        </motion.div>

        {/* Action Buttons */}
        {calculationData && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="max-w-4xl mx-auto"
          >
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 p-6 sm:p-8">
              <div className="text-center mb-6">
                <h3 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-2">
                  Your Estimate is Ready!
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Total estimated cost: <span className="text-2xl font-bold text-primary-600 dark:text-primary-400">
                    {formatZAR(calculationData.cost.total)}
                  </span>
                </p>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
                <div className="text-center p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                  <p className="text-sm text-gray-600 dark:text-gray-400">Equipment</p>
                  <p className="text-lg font-semibold text-gray-900 dark:text-white">
                    {formatZAR(calculationData.cost.equipment)}
                  </p>
                </div>
                <div className="text-center p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                  <p className="text-sm text-gray-600 dark:text-gray-400">Installation</p>
                  <p className="text-lg font-semibold text-gray-900 dark:text-white">
                    {formatZAR(calculationData.cost.installation)}
                  </p>
                </div>
                <div className="text-center p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                  <p className="text-sm text-gray-600 dark:text-gray-400">Maintenance</p>
                  <p className="text-lg font-semibold text-gray-900 dark:text-white">
                    {formatZAR(calculationData.cost.maintenance)}
                  </p>
                </div>
                <div className="text-center p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                  <p className="text-sm text-gray-600 dark:text-gray-400">Permits</p>
                  <p className="text-lg font-semibold text-gray-900 dark:text-white">
                    {formatZAR(calculationData.cost.permits)}
                  </p>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={proceedToQuote}
                  className="flex items-center justify-center space-x-2 bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-xl font-semibold transition-colors shadow-lg"
                >
                  <span>Get Detailed Quote</span>
                  <ArrowRight className="w-4 h-4" />
                </motion.button>

                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={generatePDF}
                  className="flex items-center justify-center space-x-2 bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-xl font-semibold transition-colors"
                >
                  <Download className="w-4 h-4" />
                  <span>Download PDF</span>
                </motion.button>
              </div>
            </div>
          </motion.div>
        )}

        {/* Contact Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="max-w-4xl mx-auto mt-12"
        >
          <div className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-2xl p-6 sm:p-8 text-white">
            <div className="text-center mb-6">
              <h3 className="text-xl sm:text-2xl font-bold mb-2">
                Need Help with Your Calculation?
              </h3>
              <p className="text-primary-100">
                Our experts are here to help you find the perfect solution for your needs.
              </p>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <motion.a
                href="tel:+27101096528"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="flex items-center justify-center space-x-2 bg-white/20 hover:bg-white/30 rounded-lg p-4 transition-colors"
              >
                <Phone className="w-5 h-5" />
                <span className="font-medium">Call Us</span>
              </motion.a>

              <motion.a
                href="mailto:<EMAIL>"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="flex items-center justify-center space-x-2 bg-white/20 hover:bg-white/30 rounded-lg p-4 transition-colors"
              >
                <Mail className="w-5 h-5" />
                <span className="font-medium">Email Us</span>
              </motion.a>

              <motion.a
                href="https://wa.me/27101096528"
                target="_blank"
                rel="noopener noreferrer"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="flex items-center justify-center space-x-2 bg-white/20 hover:bg-white/30 rounded-lg p-4 transition-colors"
              >
                <MessageCircle className="w-5 h-5" />
                <span className="font-medium">WhatsApp</span>
              </motion.a>
            </div>
          </div>
        </motion.div>

        {/* Features */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
          className="max-w-6xl mx-auto mt-12"
        >
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center p-6 bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700">
              <div className="bg-blue-100 dark:bg-blue-900/30 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-4">
                <CalculatorIcon className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                Real-time Pricing
              </h4>
              <p className="text-gray-600 dark:text-gray-300 text-sm">
                Get instant cost estimates as you adjust your project parameters
              </p>
            </div>

            <div className="text-center p-6 bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700">
              <div className="bg-green-100 dark:bg-green-900/30 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-4">
                <Download className="w-6 h-6 text-green-600 dark:text-green-400" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                Detailed Reports
              </h4>
              <p className="text-gray-600 dark:text-gray-300 text-sm">
                Download comprehensive cost breakdowns and project specifications
              </p>
            </div>

            <div className="text-center p-6 bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700">
              <div className="bg-purple-100 dark:bg-purple-900/30 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-4">
                <ArrowRight className="w-6 h-6 text-purple-600 dark:text-purple-400" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                Seamless Quotes
              </h4>
              <p className="text-gray-600 dark:text-gray-300 text-sm">
                Convert your estimate into a detailed quote request with one click
              </p>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default Calculator;
