/**
 * Currency utilities for South African Rand (ZAR) formatting and conversions
 */

// Exchange rate from USD to ZAR (approximate, should be updated regularly)
const USD_TO_ZAR_RATE = 19.0;

// Estimation markup percentage for quotes
const ESTIMATION_MARKUP = 0.15; // 15%

/**
 * Format amount as South African Rand currency
 */
export const formatZAR = (amount: number): string => {
  return new Intl.NumberFormat('en-ZA', {
    style: 'currency',
    currency: 'ZAR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

/**
 * Convert USD amount to ZAR with estimation markup
 */
export const convertUSDToZARWithMarkup = (usdAmount: number): number => {
  const zarAmount = usdAmount * USD_TO_ZAR_RATE;
  return Math.round(zarAmount * (1 + ESTIMATION_MARKUP));
};

/**
 * Format budget range for display (Updated for 200% price increase)
 */
export const formatBudgetRange = (budgetKey: string): string => {
  const budgetMap: { [key: string]: string } = {
    'under-600k': 'Under R600,000',
    '600k-1.5m': 'R600,000 - R1,500,000',
    '1.5m-3m': 'R1,500,000 - R3,000,000',
    '3m-6m': 'R3,000,000 - R6,000,000',
    'over-6m': 'Over R6,000,000'
  };

  return budgetMap[budgetKey] || 'Not selected';
};

/**
 * Get budget range key based on ZAR amount (Updated for 200% price increase)
 */
export const getBudgetRangeKey = (zarAmount: number): string => {
  if (zarAmount < 600000) return 'under-600k';
  if (zarAmount < 1500000) return '600k-1.5m';
  if (zarAmount < 3000000) return '1.5m-3m';
  if (zarAmount < 6000000) return '3m-6m';
  return 'over-6m';
};

/**
 * Currency conversion constants and multipliers
 */
export const CURRENCY_CONFIG = {
  USD_TO_ZAR_RATE,
  ESTIMATION_MARKUP,
  BUDGET_MULTIPLIERS: {
    'under-600k': 0.8,
    '600k-1.5m': 1.0,
    '1.5m-3m': 1.2,
    '3m-6m': 1.5,
    'over-6m': 2.0
  }
};
