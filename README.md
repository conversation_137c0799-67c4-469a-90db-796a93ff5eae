# Pazogen Water Treatment Solutions

> 📖 **[View Complete Technical Documentation](./docs/README.md)**

Pazogen is a modern web application providing a complete showcase for wastewater and water treatment solutions. Built with React, TypeScript, and Tailwind CSS, it features a beautiful, production-ready design, interactive galleries, and detailed project and service information.

## Quick Start

```bash
# Install dependencies
npm install

# Run development server
npm run dev

# Build for production
npm run build
```

## Key Features

- 🎨 **Modern UI/UX** - Responsive design with dark/light themes
- 🚀 **Performance Optimized** - Lazy loading, code splitting, PWA support
- 📱 **Mobile First** - Optimized for all device sizes
- 🎭 **Rich Animations** - Framer Motion powered interactions
- 🧮 **Advanced Calculator** - Real-time cost estimation tool
- 📊 **Interactive Gallery** - Project showcase with filtering
- 📝 **Quote System** - Integrated quote request workflow

## Tech Stack

- **Frontend**: React 18, TypeScript, Tailwind CSS
- **Animations**: Framer Motion, GSAP
- **Build Tool**: Vite
- **Icons**: Lucide React
- **Forms**: EmailJS, Formspree
- **Maps**: Google Maps API

## Documentation Structure

```
docs/
├── README.md                    # Main documentation hub
├── architecture/               # System architecture
├── components/                # Component documentation
├── development/               # Development guides
├── deployment/               # Deployment guides
└── api/                     # API documentation
```

## License

This project is Pazogen's property. Contact [<EMAIL>](mailto:<EMAIL>).

---

**Pazogen** – Leading provider of turnkey solutions for wastewater and water treatment. Powered by the Sainpse Institute of Augmented Intelligence.
