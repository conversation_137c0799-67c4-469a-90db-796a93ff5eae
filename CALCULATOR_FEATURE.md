# Advanced Quote Calculator Feature

## Overview

The Advanced Quote Calculator is a comprehensive cost estimation tool that provides real-time pricing for water treatment solutions. It integrates seamlessly with the existing quote request system and offers detailed cost breakdowns with interactive visualizations.

## Features

### 🧮 **Real-time Cost Calculation**
- Interactive parameter adjustment with instant cost updates
- Service-specific pricing models
- Dynamic multipliers based on location, urgency, and treatment level
- Comprehensive cost breakdown (equipment, installation, maintenance, permits)

### 📊 **Interactive Interface**
- **Calculator Tab**: Parameter selection and configuration
- **Cost Breakdown Tab**: Detailed cost visualization with charts
- **Comparison Tab**: Side-by-side treatment level comparisons
- Mobile-first responsive design

### 🔧 **Configurable Parameters**

#### Service Types
- Water Treatment Solutions
- Wastewater Treatment
- Screen Equipment
- Flow Control Systems
- Clarifiers
- Maintenance Services

#### Technical Parameters
- **Flow Rate**: 100 - 10,000 L/min (interactive slider)
- **Treatment Level**: Basic, Standard, Advanced, Premium
- **Location Type**: Urban, Suburban, Rural, Remote
- **Project Urgency**: Standard, Urgent, Emergency
- **Maintenance Level**: Basic, Standard, Premium

#### Additional Features
- Full Automation
- Remote Monitoring
- Backup Power
- Chemical Dosing
- pH Control
- Turbidity Monitoring

### 💰 **Pricing Logic**

#### Base Calculation
```
Base Cost = Service Base Price + (Price Per Unit × Flow Rate / 100)
```

#### Multipliers Applied
- **Treatment Level**: 0.8x - 2.0x
- **Location**: 1.0x - 1.6x
- **Urgency**: 1.0x - 1.8x
- **Additional Features**: Fixed costs added
- **Maintenance**: 10% - 25% of equipment cost

#### Cost Breakdown
- **Equipment**: Base cost with all multipliers
- **Installation**: 30% of equipment cost
- **Permits**: 5% of equipment cost
- **Maintenance**: Annual cost based on selected level

## Integration Points

### 🔗 **Navigation Integration**
- Added to main navigation menu
- Prominent button in Hero section
- Direct access from quote request form

### 📝 **Quote Request Integration**
- Calculator data automatically populates quote form
- Seamless transition from calculator to detailed quote
- Pre-filled service selection and cost estimates

### 📱 **Mobile Optimization**
- Touch-friendly interface
- Optimized for mobile screens
- Swipe gestures and responsive design

## File Structure

```
src/
├── components/ui/
│   └── AdvancedCalculator.tsx     # Main calculator component
├── pages/
│   └── Calculator/
│       └── Calculator.tsx         # Standalone calculator page
└── pages/QuoteRequest/
    └── QuoteRequest.tsx          # Enhanced with calculator integration
```

## Usage Examples

### 1. Standalone Calculator
```typescript
import AdvancedCalculator from './components/ui/AdvancedCalculator';

<AdvancedCalculator
  onCalculationChange={(params, cost) => {
    console.log('Parameters:', params);
    console.log('Cost breakdown:', cost);
  }}
  initialParams={{
    serviceType: 'water-treatment',
    flowRate: 2000,
    treatmentLevel: 'standard'
  }}
/>
```

### 2. Calculator with Quote Integration
```typescript
// Navigate to quote with calculator data
navigate('/get-a-quote', { 
  state: { 
    calculatorData: { params, cost } 
  } 
});
```

## API Reference

### CalculatorParams Interface
```typescript
interface CalculatorParams {
  serviceType: string;
  flowRate: number;
  treatmentLevel: string;
  capacity: number;
  location: string;
  urgency: string;
  additionalFeatures: string[];
  maintenanceLevel: string;
}
```

### CostBreakdown Interface
```typescript
interface CostBreakdown {
  equipment: number;
  installation: number;
  maintenance: number;
  permits: number;
  total: number;
}
```

## Customization

### Adding New Service Types
1. Update `serviceOptions` array in `AdvancedCalculator.tsx`
2. Add corresponding icons and descriptions
3. Set appropriate base prices and price-per-unit values

### Modifying Pricing Logic
1. Update multiplier objects (`locationMultipliers`, `urgencyMultipliers`, etc.)
2. Modify the `calculateCosts` function
3. Adjust cost breakdown percentages as needed

### Styling Customization
- All components use Tailwind CSS classes
- Dark mode support included
- Mobile-first responsive design
- Consistent with existing design system

## Future Enhancements

### Planned Features
- [ ] PDF quote generation
- [ ] Save/load calculator configurations
- [ ] Email quote sharing
- [ ] Advanced comparison tools
- [ ] Integration with CRM systems
- [ ] Multi-currency support
- [ ] Historical pricing data

### Technical Improvements
- [ ] Performance optimization for large datasets
- [ ] Advanced caching mechanisms
- [ ] Real-time pricing updates from API
- [ ] A/B testing for pricing strategies

## Testing

### Manual Testing Checklist
- [ ] All parameter inputs work correctly
- [ ] Cost calculations are accurate
- [ ] Mobile responsiveness
- [ ] Dark/light theme compatibility
- [ ] Integration with quote request form
- [ ] Navigation between tabs
- [ ] Error handling for edge cases

### Automated Testing
```bash
# Run component tests
npm test AdvancedCalculator

# Run integration tests
npm test Calculator
```

## Performance Considerations

- **Lazy Loading**: Calculator components are lazy-loaded
- **Memoization**: Expensive calculations are memoized
- **Debounced Updates**: Real-time updates are debounced
- **Optimized Rendering**: Minimal re-renders with React optimization

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+
- Mobile browsers (iOS Safari, Chrome Mobile)

## Accessibility

- WCAG 2.1 AA compliant
- Keyboard navigation support
- Screen reader compatibility
- High contrast mode support
- Focus management

---

**Note**: This calculator provides estimates based on standard industry pricing. Final quotes may vary based on specific project requirements, site conditions, and current market rates.
